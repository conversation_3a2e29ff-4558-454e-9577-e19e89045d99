# This file may be used to create an environment using:
# $ conda create --name <env> --file <this file>
# platform: linux-64
# created-by: conda 24.11.3
_libgcc_mutex=0.1=conda_forge
_openmp_mutex=4.5=2_gnu
aiohappyeyeballs=2.4.0=py38h06a4308_0
aiohttp=3.10.5=py38h5eee18b_0
aiosignal=1.2.0=pyhd3eb1b0_0
arrow-cpp=16.1.0=hc1eb8f0_0
async-timeout=4.0.3=py38h06a4308_0
attrs=24.2.0=py38h06a4308_0
aws-c-auth=0.6.19=h5eee18b_0
aws-c-cal=0.5.20=hdbd6064_0
aws-c-common=0.8.5=h5eee18b_0
aws-c-compression=0.2.16=h5eee18b_0
aws-c-event-stream=0.2.15=h6a678d5_0
aws-c-http=0.6.25=h5eee18b_0
aws-c-io=0.13.10=h5eee18b_0
aws-c-mqtt=0.7.13=h5eee18b_0
aws-c-s3=0.1.51=hdbd6064_0
aws-c-sdkutils=0.1.6=h5eee18b_0
aws-checksums=0.1.13=h5eee18b_0
aws-crt-cpp=0.18.16=h6a678d5_0
aws-sdk-cpp=1.10.55=h721c034_0
blas=1.0=mkl
blas-devel=3.9.0=16_linux64_mkl
boost-cpp=1.82.0=hdb19cb5_2
bottleneck=1.3.7=py38ha9d4c09_0
brotli=1.0.9=h5eee18b_9
brotli-bin=1.0.9=h5eee18b_9
brotli-python=1.0.9=py38hfa26641_7
bzip2=1.0.8=h5eee18b_6
c-ares=1.19.1=h5eee18b_0
ca-certificates=2025.2.25=h06a4308_0
cachetools=4.2.1=pypi_0
certifi=2024.8.30=py38h06a4308_0
cffi=1.17.0=py38heb5c249_0
charset-normalizer=3.4.0=pyhd8ed1ab_0
colorama=0.4.6=pyhd8ed1ab_0
contourpy=1.0.5=py38hdb19cb5_0
cuda-cudart=12.4.127=h99ab3db_0
cuda-cudart_linux-64=12.4.127=hd681fbe_0
cuda-cupti=12.4.127=h6a678d5_1
cuda-libraries=12.4.1=h06a4308_1
cuda-nvrtc=12.4.127=h99ab3db_1
cuda-nvtx=12.4.127=h6a678d5_1
cuda-opencl=12.4.127=h6a678d5_0
cuda-runtime=12.4.1=hb982923_0
cuda-version=12.4=hbda6634_3
cycler=0.11.0=pyhd3eb1b0_0
cyrus-sasl=2.1.28=h52b45da_1
datasets=2.19.1=py38h06a4308_0
dbus=1.13.18=hb2f20db_0
dill=0.3.8=py38h06a4308_0
drain3=0.9.11=pypi_0
expat=2.6.4=h6a678d5_0
filelock=3.13.1=py38h06a4308_0
fontconfig=2.14.1=h55d465d_3
fonttools=4.51.0=py38h5eee18b_0
freetype=2.12.1=h4a9f257_0
frozenlist=1.4.0=py38h5eee18b_0
fsspec=2024.3.1=py38h06a4308_0
gflags=2.2.2=h6a678d5_1
glib=2.78.4=h6a678d5_0
glib-tools=2.78.4=h6a678d5_0
glog=0.5.0=h6a678d5_1
gmp=6.3.0=h6a678d5_0
gmpy2=2.1.2=py38heeb90bb_0
gst-plugins-base=1.14.1=h6a678d5_1
gstreamer=1.14.1=h5eee18b_1
h2=4.1.0=pyhd8ed1ab_0
hdbscan=0.8.27=py38h5c078b8_0
hpack=4.0.0=pyh9f0ad1d_0
huggingface_hub=0.24.6=py38h06a4308_0
hyperframe=6.0.1=pyhd8ed1ab_0
icu=73.1=h6a678d5_0
idna=3.10=pyhd8ed1ab_0
importlib_resources=6.4.0=py38h06a4308_0
intel-openmp=2022.1.0=h9e868ea_3769
jinja2=3.1.4=py38h06a4308_0
joblib=1.1.0=pyhd3eb1b0_0
jpeg=9e=h5eee18b_3
jsonpickle=1.5.1=pypi_0
kiwisolver=1.4.4=py38h6a678d5_0
krb5=1.20.1=h143b758_1
lcms2=2.16=hb9589c4_0
ld_impl_linux-64=2.40=h12ee557_0
lerc=4.0.0=h6a678d5_0
libabseil=20240116.2=cxx17_h6a678d5_0
libblas=3.9.0=16_linux64_mkl
libboost=1.82.0=h109eef0_2
libbrotlicommon=1.0.9=h5eee18b_9
libbrotlidec=1.0.9=h5eee18b_9
libbrotlienc=1.0.9=h5eee18b_9
libcblas=3.9.0=16_linux64_mkl
libclang=14.0.6=default_hc6dbbc7_2
libclang13=14.0.6=default_he11475f_2
libcublas=12.4.5.8=h99ab3db_1
libcufft=11.2.1.3=h99ab3db_1
libcufile=1.9.1.3=h99ab3db_1
libcups=2.4.2=h2d74bed_1
libcurand=10.3.5.147=h99ab3db_1
libcurl=8.12.1=hc9e6f67_0
libcusolver=11.6.1.9=h99ab3db_1
libcusparse=12.3.1.170=h99ab3db_1
libdeflate=1.22=h5eee18b_0
libedit=3.1.20230828=h5eee18b_0
libev=4.33=h7f8727e_1
libevent=2.1.12=hdbd6064_1
libffi=3.4.4=h6a678d5_1
libgcc=14.2.0=h77fa898_1
libgcc-ng=14.2.0=h69a702a_1
libgfortran=14.2.0=h69a702a_1
libgfortran-ng=14.2.0=h69a702a_1
libgfortran5=14.2.0=hd5240d6_1
libglib=2.78.4=hdc74915_0
libgomp=14.2.0=h77fa898_1
libgrpc=1.62.2=h2d74bed_0
libiconv=1.16=h5eee18b_3
liblapack=3.9.0=16_linux64_mkl
liblapacke=3.9.0=16_linux64_mkl
libllvm14=14.0.6=hecde1de_4
libnghttp2=1.57.0=h2d74bed_0
libnpp=12.2.5.30=h99ab3db_1
libnvfatbin=12.4.127=h7934f7d_2
libnvjitlink=12.4.127=h99ab3db_1
libnvjpeg=12.3.1.117=h6a678d5_1
libopenblas=0.3.21=h043d6bf_0
libpng=1.6.39=h5eee18b_0
libpq=17.4=hdbd6064_0
libprotobuf=4.25.3=he621ea3_0
libssh2=1.11.1=h251f7ec_0
libstdcxx-ng=11.2.0=h1234567_1
libthrift=0.15.0=h1795dd8_2
libtiff=4.5.1=hffd6297_1
libuuid=1.41.5=h5eee18b_0
libwebp-base=1.3.2=h5eee18b_1
libxcb=1.15=h7f8727e_0
libxkbcommon=1.0.1=h097e994_2
libxml2=2.13.5=hfdd30dd_0
llvm-openmp=14.0.6=h9e868ea_0
lz4-c=1.9.4=h6a678d5_1
markupsafe=2.1.3=py38h5eee18b_0
matplotlib=3.7.2=py38h06a4308_0
matplotlib-base=3.7.2=py38h1128e8f_0
mkl=2022.1.0=hc2b9512_224
mkl-devel=2022.1.0=h66538d2_224
mkl-include=2022.1.0=h06a4308_224
mpc=1.3.1=h5eee18b_0
mpfr=4.2.1=h5eee18b_0
mpmath=1.3.0=py38h06a4308_0
multidict=6.0.4=py38h5eee18b_0
multiprocess=0.70.15=py38h06a4308_0
mysql=8.4.0=h29a9f33_1
ncurses=6.4=h6a678d5_0
networkx=3.1=py38h06a4308_0
numexpr=2.7.3=py38h43a58ef_1
numpy=1.22.3=py38h99721a1_2
ocl-icd=2.3.2=h5eee18b_1
openblas=0.3.4=ha44fe06_0
openjpeg=2.5.2=he7f1fd0_0
openldap=2.6.4=h42fbc30_0
openssl=3.4.1=h7b32b05_0
orc=2.0.1=h2d29ad5_0
overrides=6.1.0=pyhd8ed1ab_0
packaging=24.2=pyhd8ed1ab_2
pandas=2.0.3=py38h1128e8f_0
pcre2=10.42=hebb0a14_1
pillow=10.4.0=py38h5eee18b_0
pip=24.2=py38h06a4308_0
platformdirs=4.3.6=pyhd8ed1ab_0
ply=3.11=py38_0
pooch=1.8.2=pyhd8ed1ab_0
pyarrow=16.1.0=py38h1128e8f_0
pycparser=2.22=pyhd8ed1ab_0
pyparsing=3.0.9=py38h06a4308_0
pyqt=5.15.10=py38h6a678d5_0
pyqt5-sip=12.13.0=py38h5eee18b_0
pysocks=1.7.1=pyha2e5f31_6
python=3.8.20=he870216_0
python-dateutil=2.9.0post0=py38h06a4308_2
python-tzdata=2023.3=pyhd3eb1b0_0
python-xxhash=2.0.2=py38h5eee18b_1
python_abi=3.8=2_cp38
pytorch=2.4.1=py3.8_cuda12.4_cudnn9.1.0_0
pytorch-cuda=12.4=hc786d27_7
pytorch-model-summary=0.1.1=py_0
pytorch-mutex=1.0=cuda
pytz=2025.1=pypi_0
pyyaml=6.0.2=py38h5eee18b_0
qt-main=5.15.2=hb6262e9_12
re2=2022.04.01=h295c915_0
readline=8.2=h5eee18b_0
regex=2024.7.24=py38h2019614_0
requests=2.32.3=pyhd8ed1ab_0
s2n=1.3.27=hdbd6064_0
safetensors=0.4.5=py38ha89cbab_0
scikit-learn=0.24.0=py38h658cfdd_0
scipy=1.8.1=py38h1ee437e_0
setuptools=75.1.0=py38h06a4308_0
sip=6.7.12=py38h6a678d5_0
six=1.16.0=pyh6c4a22f_0
snappy=1.2.1=h6a678d5_0
sqlite=3.45.3=h5eee18b_0
sympy=1.13.3=py38h06a4308_0
threadpoolctl=3.5.0=pyhc1e730c_0
tk=8.6.14=h39e8969_0
tokenizers=0.20.1=py38h7d74088_0
tomli=2.0.1=py38h06a4308_0
torchinfo=1.8.0=pyhd8ed1ab_0
torchtriton=3.0.0=py38
tornado=6.4.1=py38h5eee18b_0
tqdm=4.67.1=pyhd8ed1ab_0
transformers=4.45.2=py38h06a4308_0
typing-extensions=4.11.0=py38h06a4308_0
typing_extensions=4.11.0=py38h06a4308_0
typing_utils=0.1.0=pyhd8ed1ab_0
tzdata=2025.1=pypi_0
unicodedata2=15.1.0=py38h5eee18b_0
urllib3=2.2.3=pyhd8ed1ab_0
utf8proc=2.6.1=h5eee18b_1
wheel=0.44.0=py38h06a4308_0
xxhash=0.8.0=h7f8727e_3
xz=5.4.6=h5eee18b_1
yaml=0.2.5=h7b6447c_0
yarl=1.11.0=py38h5eee18b_0
zipp=3.20.2=py38h06a4308_0
zlib=1.2.13=h5eee18b_1
zstandard=0.23.0=py38h62bed22_0
zstd=1.5.6=hc292b87_0
