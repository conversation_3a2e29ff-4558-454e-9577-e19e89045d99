from tensorflow.keras import layers
from tensorflow import keras

def autoencoder(input_dim=10, max_len=10, hidden_size=[30, 10, 5]):
    encoder = keras.Sequential()
    decoder = keras.Sequential()

    encoder.append(layers.InputLayer(input_shape=(input_dim, )))

    for i in range(len(hidden_size)):
        encoder.append(layers.Dense(hidden_size[i], activation='relu'))
        decoder.append(layers.Dense(hidden_size[-i-1], activation='relu'))
    
    decoder.append(layers.Dense(input_dim))

    model = keras.Model(encoder, decoder)

    return model

from tensorflow import keras
from tensorflow.keras import layers
from .positional_encodings import PositionEmbedding

# embed_dim = 768  # Embedding size for each token
# num_heads = 12  # Number of attention heads
# ff_dim = 2048  # Hidden layer size in feed forward network inside transformer
# max_len = 20

class TransformerBlock(layers.Layer):
    def __init__(self, embed_dim, num_heads, ff_dim, rate=0.1):
        super(TransformerBlock, self).__init__()
        self.att = layers.MultiHeadAttention(num_heads=num_heads, key_dim=embed_dim)
        self.ffn = keras.Sequential(
            [layers.Dense(ff_dim, activation="relu"), layers.Dense(embed_dim), ]
        )
        self.layernorm1 = layers.LayerNormalization(epsilon=1e-6)
        self.layernorm2 = layers.LayerNormalization(epsilon=1e-6)
        self.dropout1 = layers.Dropout(rate)
        self.dropout2 = layers.Dropout(rate)

    def call(self, inputs, training):
        attn_output = self.att(inputs, inputs)
        attn_output = self.dropout1(attn_output, training=training)
        out1 = self.layernorm1(inputs + attn_output)
        ffn_output = self.ffn(out1)
        ffn_output = self.dropout2(ffn_output, training=training)
        return self.layernorm2(out1 + ffn_output)


def transformer_ae(embed_dim, ff_dim, max_len, num_heads, dropout=0.1):
    inputs = layers.Input(shape=(max_len, embed_dim))
    
    transformer_block = TransformerBlock(embed_dim, num_heads, ff_dim)
    embedding_layer = PositionEmbedding(1024, embed_dim)
    x = embedding_layer(inputs)
    x = transformer_block(x)

    x = layers.Dense(256, activation="relu")(x)
    x = layers.Dense(128, activation="relu")(x)
    x = layers.Dense(64, activation="relu")(x)
    x = layers.Dense(128, activation="relu")(x)
    x = layers.Dense(256, activation="relu")(x)
    outputs = layers.Dense(768, activation="tanh")(x)

    model = keras.Model(inputs=inputs, outputs=outputs)
    return model

def transformer_sae(embed_dim, ff_dim, max_len, num_heads, dropout=0.1):
    inputs = layers.Input(shape=(max_len, embed_dim))
    transformer_block = TransformerBlock(embed_dim, num_heads, ff_dim)
    embedding_layer = PositionEmbedding(1024, embed_dim)
    x = embedding_layer(inputs)
    x = transformer_block(x)

    x = layers.Dense(256, activation="relu")(x)
    x = layers.Dense(128, activation="relu")(x)
    latent = layers.Dense(64, activation="relu")(x)
    x = layers.Dense(128, activation="relu")(latent)
    x = layers.Dense(256, activation="relu")(x)
    outputs = layers.Dense(768, activation="tanh")(x)

    model = keras.Model(inputs=inputs, outputs=outputs)
    return model, latent