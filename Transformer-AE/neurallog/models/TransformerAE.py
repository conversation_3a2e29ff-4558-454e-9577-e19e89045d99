import numpy as np
import tensorflow as tf
from tensorflow.keras import layers, Model
from tensorflow.keras.initializers import GlorotUniform

def get_angles(pos, i, d_model):
    angle_rates = 1 / np.power(10000, (2 * (i//2)) / np.float32(d_model))
    return pos * angle_rates

def positional_encoding(position, d_model):
    angle_rads = get_angles(np.arange(position)[:, np.newaxis],
                            np.arange(d_model)[np.newaxis, :],
                            d_model)

    # apply sin to even indices in the array; 2i
    angle_rads[:, 0::2] = np.sin(angle_rads[:, 0::2])

    # apply cos to odd indices in the array; 2i+1
    angle_rads[:, 1::2] = np.cos(angle_rads[:, 1::2])

    pos_encoding = angle_rads[np.newaxis, ...]

    return tf.cast(pos_encoding, dtype=tf.float32)

class PositionalEncoding(layers.Layer):
    def __init__(self, max_len, embed_dim):
        super(PositionalEncoding, self).__init__()
        self.pos_encoding = positional_encoding(max_len,
                                                embed_dim)

    @tf.function
    def call(self, x):
        seq_len = tf.shape(x)[1]
        x += self.pos_encoding[:, :seq_len, :]
        return x


class TransformerAEWithPureDecoder(Model):
    def __init__(self, embed_dim=768, num_heads=12, hidden_dims=[256, 128], latent_dim=64, \
                 num_layers=1, max_seq_length=512):
        super(TransformerAEWithPureDecoder, self).__init__()
        
        # Xavier initializer
        self.initializer = GlorotUniform(seed=42)

        # Input layer
        self.input_layer = layers.InputLayer(input_shape=(max_seq_length, embed_dim))
        
        # Positional encoding
        self.positional_encoding = PositionalEncoding(max_seq_length, embed_dim)
        
        # Transformer Encoder layers
        self.encoder_layers = [
            [
                layers.MultiHeadAttention(num_heads=num_heads, key_dim=embed_dim, \
                                          kernel_initializer=self.initializer, bias_initializer=self.initializer),
                layers.LayerNormalization(epsilon=1e-6),
                # layers.Dense(hidden_dim, activation='relu'),
                layers.Dense(embed_dim, activation='relu', \
                             kernel_initializer=self.initializer, bias_initializer=self.initializer),
                layers.LayerNormalization(epsilon=1e-6)
            ] for _ in range(num_layers)
        ]
        
        # Latent space layers
        self.fc_encoder_hidden_layers = [layers.Dense(hidden_dim, activation='relu', \
                                                      kernel_initializer=self.initializer, bias_initializer=self.initializer) \
                                 for hidden_dim in hidden_dims]

        self.fc_latent = layers.Dense(latent_dim, kernel_initializer=self.initializer, bias_initializer=self.initializer)

        self.fc_decoder_hidden_layers = [layers.Dense(hidden_dim, activation='relu', \
                                                      kernel_initializer=self.initializer, bias_initializer=self.initializer) \
                                    for hidden_dim in hidden_dims[::-1]]
        
        # self.fc_decoder_hidden_layers.append(layers.Dense(embed_dim))
   
        # Output layer
        self.fc_out = layers.Dense(embed_dim, kernel_initializer=self.initializer, bias_initializer=self.initializer)
        
    @tf.function
    def call(self, inputs):
        # Encoder part
        x = self.input_layer(inputs)
        x = self.positional_encoding(x)
        
        for mha, norm1, dense_ffn, norm2 in self.encoder_layers:
            attn_output = mha(x, x)
            x = norm1(x + attn_output)
            ffn_output = dense_ffn(x)
            x = norm2(x + ffn_output)
        
        encoder_output_memory = tf.identity(x)

        # Latent space
        for hidden_layer in self.fc_encoder_hidden_layers:
            x = hidden_layer(x)
        
        latent = self.fc_latent(x)
        x = tf.identity(latent)
        # Decoder part
        for hidden_layer in self.fc_decoder_hidden_layers:
            x = hidden_layer(x)

        # Output layer
        output = self.fc_out(x)
        return output

class AttentionApplication(layers.Layer):
    def __init__(self):
        super().__init__()
    
    @tf.function
    def call(self, decoder_output, encoder_attention):
        # Apply encoder attention to decoder output
        # decoder_output shape: (batch_size, seq_len, d_model)
        # encoder_attention shape: (batch_size, num_heads, seq_len, seq_len)
        
        # Average attention across heads
        mean_attention = tf.reduce_mean(encoder_attention, axis=1)  # (batch_size, seq_len, seq_len)
        
        # Apply attention to decoder output
        attended_output = tf.matmul(mean_attention, decoder_output)
        
        return attended_output

class TransformerAEWithResidualAttention(Model):
    def __init__(self, embed_dim=768, num_heads=12, hidden_dims=[256, 128], latent_dim=64, \
                 num_layers=1, max_seq_length=512, attention_term=1.0):
        super(TransformerAEWithResidualAttention, self).__init__()
        
        # Xavier initializer
        self.initializer = GlorotUniform(seed=42)

        self.attention_term = attention_term

        # Input layer
        self.input_layer = layers.InputLayer(input_shape=(max_seq_length, embed_dim))
        
        # Positional encoding
        self.positional_encoding = PositionalEncoding(max_seq_length, embed_dim)
        
        # Transformer Encoder layers
        self.encoder_layers = [
            [
                layers.MultiHeadAttention(num_heads=num_heads, key_dim=embed_dim, \
                    kernel_initializer=self.initializer, bias_initializer=self.initializer),
                layers.LayerNormalization(epsilon=1e-6),
                # layers.Dense(hidden_dim, activation='relu'),
                layers.Dense(embed_dim, activation='relu', \
                             kernel_initializer=self.initializer, bias_initializer=self.initializer),
                layers.LayerNormalization(epsilon=1e-6)
            ] for _ in range(num_layers)
        ]
        
        # Latent space layers
        # self.fc_latent_input_1 = layers.Dense(hidden_dim, activation='relu')
        # self.fc_latent_input_2 = layers.Dense(hidden_dim, activation='relu')
        
        self.fc_encoder_hidden_layers = [layers.Dense(hidden_dim, activation='relu', \
                                                      kernel_initializer=self.initializer, bias_initializer=self.initializer) \
                                 for hidden_dim in hidden_dims]

        self.fc_latent = layers.Dense(latent_dim, kernel_initializer=self.initializer, bias_initializer=self.initializer)

        self.fc_decoder_hidden_layers = [layers.Dense(hidden_dim, activation='relu', \
            kernel_initializer=self.initializer, bias_initializer=self.initializer) \
                                    for hidden_dim in hidden_dims[::-1]]
        
        self.fc_decoder_hidden_layers.append(layers.Dense(embed_dim, kernel_initializer=self.initializer, bias_initializer=self.initializer))
        
        # Output layer
        self.fc_out = layers.Dense(embed_dim, kernel_initializer=self.initializer, bias_initializer=self.initializer)
        
    @tf.function
    def call(self, inputs):
        # Encoder part
        x = self.input_layer(inputs)
        x = self.positional_encoding(x)
        
        for mha, norm1, dense_ffn, norm2 in self.encoder_layers:
            encoder_attn_output, encoder_attn_matrix = mha(x, x, return_attention_scores=True)
            x = norm1(x + encoder_attn_output)
            ffn_output = dense_ffn(x)
            x = norm2(x + ffn_output)
        
        # Latent space
        for hidden_layer in self.fc_encoder_hidden_layers:
            x = hidden_layer(x)
        
        latent = self.fc_latent(x)
        x = tf.identity(latent)
        # Decoder part
        for hidden_layer in self.fc_decoder_hidden_layers:
            x = hidden_layer(x)

        attention_application = AttentionApplication()
        attended_output = attention_application(x, encoder_attn_matrix)

        x_res = self.fc_out(attended_output)

        return x_res
    

class TransformerAEWithMaskedAttention(Model):
    def __init__(self, embed_dim=768, num_heads=12, hidden_dims=[256, 128], latent_dim=64, \
                 num_layers=1, max_seq_length=512):
        super(TransformerAEWithMaskedAttention, self).__init__()
        
        # Xavier initializer
        initializer = GlorotUniform()

        # Input layer
        self.input_layer = layers.InputLayer(input_shape=(max_seq_length, embed_dim))
        
        # Positional encoding
        self.positional_encoding = PositionalEncoding(embed_dim, max_seq_length)
        
        # Transformer Encoder layers
        self.encoder_layers = [
            [
                layers.MultiHeadAttention(num_heads=num_heads, key_dim=embed_dim),
                layers.LayerNormalization(epsilon=1e-6),
                # layers.Dense(hidden_dim, activation='relu'),
                layers.Dense(embed_dim, activation='relu'),
                layers.LayerNormalization(epsilon=1e-6)
            ] for _ in range(num_layers)
        ]
        
        # Latent space layers
        # self.fc_latent_input_1 = layers.Dense(hidden_dim, activation='relu')
        # self.fc_latent_input_2 = layers.Dense(hidden_dim, activation='relu')
        
        self.fc_encoder_hidden_layers = [layers.Dense(hidden_dim, activation='relu') \
                                 for hidden_dim in hidden_dims]

        self.fc_latent = layers.Dense(latent_dim)

        self.fc_decoder_hidden_layers = [layers.Dense(hidden_dim, activation='relu') \
                                    for hidden_dim in hidden_dims[::-1]]
        
        self.fc_decoder_hidden_layers.append(layers.Dense(embed_dim))
        # Decoder layers
        self.decoder_layers = [
            [
                layers.MultiHeadAttention(num_heads=num_heads, key_dim=embed_dim),  # Masked self-attention
                layers.LayerNormalization(epsilon=1e-6),
                layers.MultiHeadAttention(num_heads=num_heads, key_dim=embed_dim),  # Cross-attention with encoder memory
                layers.LayerNormalization(epsilon=1e-6),
                # layers.Dense(hidden_dim, activation='relu'),
                layers.Dense(embed_dim, activation='relu'),
                layers.LayerNormalization(epsilon=1e-6)
            ] for _ in range(num_layers)
        ]
        
        # Output layer
        self.fc_out = layers.Dense(embed_dim)
        
    @tf.function
    def call(self, inputs):
        # Encoder part
        x = self.input_layer(inputs)
        print(x.shape)
        x = self.positional_encoding(x)
        
        for mha, norm1, dense_ffn, norm2 in self.encoder_layers:
            attn_output = mha(x, x)
            x = norm1(x + attn_output)
            ffn_output = dense_ffn(x)
            x = norm2(x + ffn_output)
        
        encoder_output_memory = tf.identity(x)

        # Latent space
        for hidden_layer in self.fc_encoder_hidden_layers:
            x = hidden_layer(x)
        
        latent = self.fc_latent(x)
        x = tf.identity(latent)
        # Decoder part
        for hidden_layer in self.fc_decoder_hidden_layers:
            x = hidden_layer(x)

        # x += self.positional_encoding.pos_encoding

        for masked_mha, norm1, cross_mha, norm2, dense_ffn, norm3 in self.decoder_layers:
            attn_output = masked_mha(x, x, \
                                     attention_mask=self._create_look_ahead_mask(tf.shape(x)[1]))
            decoder_output = norm1(x + attn_output)
            
            cross_attn_output = cross_mha(decoder_output, encoder_output_memory)
            decoder_output = norm2(decoder_output + cross_attn_output)
            
            ffn_output = dense_ffn(dense_ffn(decoder_output))
            decoder_output = norm3(decoder_output + ffn_output)
        
        # Output layer
        output = self.fc_out(decoder_output)
        return output

    def _create_look_ahead_mask(self, seq_len):
        mask = tf.linalg.band_part(tf.ones((seq_len, seq_len)), -1, 0)
        return 1 - mask  # Invert the mask (1 = mask, 0 = no mask)

# Test the model
if __name__ == "__main__":
    # Instantiate the model
    input_dim = 10000        # Vocabulary size
    embed_dim = 128          # Embedding dimension
    num_heads = 8            # Number of attention heads
    num_layers = 4           # Number of transformer encoder/decoder layers
    max_seq_length = 512     # Max sequence length

    model = TransformerAEWithResidualAttention()
    model.compile(optimizer="adam", loss=[tf.keras.losses.MeanSquaredError()])

    # Sample input: batch of 32 sequences of length 20
    sample_input = tf.random.uniform((100, 50, 768), maxval=input_dim, dtype=tf.float32)
    output = model(sample_input)
    print(model.summary())
    print("Output shape:", output.shape)