import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from .positional_encodings import PositionEmbedding

# embed_dim = 768  # Embedding size for each token
# num_heads = 12  # Number of attention heads
# ff_dim = 2048  # Hidden layer size in feed forward network inside transformer
# max_len = 20

class TransformerBlock(layers.Layer):
    def __init__(self, embed_dim, num_heads, ff_dim, rate=0.1):
        super(TransformerBlock, self).__init__()
        self.att = layers.MultiHeadAttention(num_heads=num_heads, key_dim=embed_dim)
        self.ffn = keras.Sequential(
            [layers.Dense(ff_dim, activation="tanh"), layers.Dense(embed_dim), ]
        )
        self.layernorm1 = layers.LayerNormalization(epsilon=1e-6)
        self.layernorm2 = layers.LayerNormalization(epsilon=1e-6)
        self.dropout1 = layers.Dropout(rate)
        self.dropout2 = layers.Dropout(rate)

    def call(self, inputs, training):
        attn_output = self.att(inputs, inputs)
        attn_output = self.dropout1(attn_output, training=training)
        out1 = self.layernorm1(inputs + attn_output)
        ffn_output = self.ffn(out1)
        ffn_output = self.dropout2(ffn_output, training=training)
        return self.layernorm2(out1 + ffn_output)


def transformer_sae(embed_dim, ff_dim, max_len, num_heads, dropout=0.1, shrink_lambda=0.2):
    inputs = layers.Input(shape=(max_len, embed_dim))
    embedding_layer = PositionEmbedding(1024, embed_dim)
    transformer_block = TransformerBlock(embed_dim, num_heads, ff_dim)
    x = embedding_layer(inputs)
    x = transformer_block(x)
    # context_latent = transformer_block(x)
    # x = layers.Dense(256, activation="tanh")(x)
    # x = layers.Dense(128, activation="tanh")(x)
    # outputs = layers.Dense(768)(x)

    x = layers.Dense(256, activation="tanh")(x)
    x = layers.Dense(128, activation="tanh")(x)
    latent = layers.Dense(64)(x)
    x = layers.Dense(128, activation="tanh")(latent)
    x = layers.Dense(256, activation="tanh")(x)
    outputs = layers.Dense(768)(x)

    model = keras.Model(inputs=inputs, outputs=[outputs, latent])

    return model


class TransformerSAE(keras.Model):
    def __init__(self, embed_dim, ff_dim, max_len, num_heads, dropout=0.1, shrink_lambda=0.2):
        super(TransformerSAE, self).__init__()
        self.shrink_lambda = shrink_lambda
        self.encoder = keras.Sequential([
            layers.Input(shape=(max_len, embed_dim)),
            PositionEmbedding(1024, embed_dim),
            TransformerBlock(embed_dim, num_heads, ff_dim),
            layers.Dense(256, activation="tanh"),
            layers.Dense(128, activation="tanh"),
            layers.Dense(64, activation="tanh")
        ])
        self.decoder = keras.Sequential([
            layers.Dense(128, activation="tanh"),
            layers.Dense(256, activation="tanh"),
            layers.Dense(768, activation="tanh")
        ])

    def encode(self, inputs):
        latent = self.encoder(inputs)
        return latent

    def call(self, inputs):
        latent = self.encoder(inputs)
        self.latent = latent
        reconstruction = self.decoder(latent)

        return reconstruction, latent
    
