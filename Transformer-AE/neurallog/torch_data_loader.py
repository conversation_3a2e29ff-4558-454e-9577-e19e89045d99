import os
import re
import gc
import pickle
import string
import random
import numpy as np
import pandas as pd
from collections import OrderedDict
from datetime import datetime
from sklearn.utils import shuffle
from transformers import BertTokenizer, BertModel
import torch
from torch.utils.data import Dataset, DataLoader
import time
from tqdm import tqdm

# ==========================
# Utility Functions
# ==========================
def clean(s):
    """ Preprocess log message
    Parameters
    ----------
    s: str, raw log message

    Returns
    -------
    str, preprocessed log message without number tokens and special characters
    """
    s = re.sub('\]|\[|\)|\(|\=|\,|\;', ' ', s)
    s = " ".join([word.lower() if word.isupper() else word for word in s.strip().split()])
    s = re.sub('([A-Z][a-z]+)', r' \1', re.sub('([A-Z]+)', r' \1', s))
    s = " ".join([word for word in s.split() if not bool(re.search(r'\d', word))])
    trantab = str.maketrans(dict.fromkeys(list(string.punctuation)))
    content = s.translate(trantab)
    s = " ".join([word.lower().strip() for word in content.strip().split()])
    return s

def bert_encoder(s, bert_tokenizer, bert_model, no_wordpiece=0):
    """ Compute semantic vector with BERT
    Parameters
    ----------
    s: string to encode
    no_wordpiece: 1 if you do not use sub-word tokenization, otherwise 0

    Returns
    -------
        np array in shape of (768,)
    """
    if no_wordpiece:
        words = s.split(" ")
        words = [word for word in words if word in bert_tokenizer.vocab.keys()]
        s = " ".join(words)
    inputs = bert_tokenizer(s, return_tensors='pt', max_length=512, truncation=True, padding='max_length')
    outputs = bert_model(**inputs)
    v = torch.mean(outputs.last_hidden_state, 1)
    return v[0].detach().numpy()

# ==========================
# Data Loading Functions
# ==========================
def load_supercomputers(log_file, train_ratio=0.8, oov_set_ratio=None, windows_size=20, 
                        step_size=0, e_type='bert', mode="balance", no_word_piece=0):
    """ Load BGL, Thunderbird, and Spirit unstructured log into train and test data
    Parameters
    ----------
    log_file: str, the file path of raw log (extension: .log).
    train_ratio: float, the ratio of training data for train/test split.
    oov_set_ratio: float, the ratio of testing data to use (must be <= 1-train_ratio)
    windows_size: int, the window size for sliding window
    step_size: int, the step size for sliding window. if step_size is equal to window_size then fixed window is applied.
    e_type: str, embedding type (choose from BERT, XLM, and GPT2).
    mode: str, split train/testing in balance or not
    no_word_piece: bool, use split word into wordpiece or not.
    """
    # Validate oov_set_ratio
    if oov_set_ratio is not None:
        if train_ratio + oov_set_ratio > 1.0:
            raise ValueError(f"train_ratio ({train_ratio}) + oov_set_ratio ({oov_set_ratio}) must not exceed 1.0")
    
    print("Loading", log_file)
    with open(log_file, mode="r", encoding='iso-8859-1') as f:
        logs = f.readlines()
        logs = [x.strip() for x in logs]
    
    # Split data into train and test
    if mode == "balance":
        # Original balanced splitting logic...
        pass
    else:
        # Sequential split
        if oov_set_ratio is None:
            # Use all remaining data for testing (original behavior)
            train_logs = logs[:int(len(logs) * train_ratio)]
            test_logs = logs[int(len(logs) * train_ratio):]
        else:
            # Use only a portion of the remaining data for testing
            train_end_idx = int(len(logs) * train_ratio)
            test_end_idx = train_end_idx + int(len(logs) * oov_set_ratio)
            train_logs = logs[:train_end_idx]
            test_logs = logs[train_end_idx:test_end_idx]
            
            # Log the data split information
            print(f"Using {len(train_logs)} logs for training ({train_ratio*100:.1f}% of total)")
            print(f"Using {len(test_logs)} logs for testing ({oov_set_ratio*100:.1f}% of total)")
    
    # Group logs by node_id for training
    train_nodes = {}
    for line in train_logs:
        tokens = line.strip().split()
        node = str(tokens[3])
        if node not in train_nodes:
            train_nodes[node] = []
        train_nodes[node].append(line)
    
    # Group logs by node_id for testing
    test_nodes = {}
    for line in test_logs:
        tokens = line.strip().split()
        node = str(tokens[3])
        if node not in test_nodes:
            test_nodes[node] = []
        test_nodes[node].append(line)
    
    # Select encoder based on e_type
    E = {}
    e_type = e_type.lower()
    if e_type == "bert":
        bert_tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')
        bert_model = BertModel.from_pretrained('bert-base-uncased')
        encoder = bert_encoder
    else:
        raise ValueError('Embedding type {0} is not supported'.format(e_type.upper()))
    
    def process_logs(nodes, windows_size, step_size):
        x_data, y_data = [], []
        for node, node_logs in tqdm(nodes.items(), desc="Processing nodes"):
            i = 0
            while i < len(node_logs) - windows_size:
                seq = []
                label = 0
                for j in range(i, i + windows_size):
                    if node_logs[j][0] != "-":
                        label = 1
                    content = node_logs[j]
                    content = content[content.find(' ') + 1:]
                    content = clean(content.lower())
                    if content not in E:
                        try:
                            E[content] = encoder(content, bert_tokenizer, bert_model, no_word_piece)
                        except Exception as _:
                            print(content)
                    seq.append(E[content])
                x_data.append(seq.copy())
                y_data.append(label)
                i += step_size
        return x_data, y_data
    
    # Process logs for training and testing
    print("Processing training data")
    x_tr, y_tr = process_logs(train_nodes, windows_size, step_size)
    print("Processing testing data")
    x_te, y_te = process_logs(test_nodes, windows_size, step_size)
    
    # Shuffle training data
    (x_tr, y_tr) = shuffle(x_tr, y_tr)
    
    print(f"Training samples: {len(x_tr)}, Testing samples: {len(x_te)}")
    # Print normal vs abnormal samples in testing set
    normal_count = sum(1 for label in y_te if label == 0)
    abnormal_count = sum(1 for label in y_te if label == 1)
    print(f"Testing set: {normal_count} normal samples, {abnormal_count} abnormal samples")
    del logs, train_logs, test_logs, train_nodes, test_nodes, E, encoder, bert_tokenizer, bert_model
    gc.collect()

    return (x_tr, y_tr), (x_te, y_te)
