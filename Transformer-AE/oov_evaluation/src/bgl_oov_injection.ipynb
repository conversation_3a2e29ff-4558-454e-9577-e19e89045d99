{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["rested_data = \"../data/raw_data/BGL/processed/rested.csv\"\n", "train_data = \"../data/raw_data/BGL/processed/training.csv\"\n", "test_data = \"../data/raw_data/BGL/processed/testing.csv\"\n", "\n", "df_rested = pd.read_csv(rested_data)\n", "df_train = pd.read_csv(train_data)\n", "df_test = pd.read_csv(test_data)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>LineID</th>\n", "      <th>Label</th>\n", "      <th>OOV_weight</th>\n", "      <th>Content</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>- 1117838570 2005.06.03 R02-M1-N0-C:J12-U11 20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>- 1117838570 2005.06.03 R02-M1-N0-C:J12-U11 20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>- 1117838570 2005.06.03 R02-M1-N0-C:J12-U11 20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>- 1117838570 2005.06.03 R02-M1-N0-C:J12-U11 20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>- 1117838570 2005.06.03 R02-M1-N0-C:J12-U11 20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>799995</th>\n", "      <td>799995</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>- 1118843881 2005.06.15 R21-M0-N4-C:J03-U01 20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>799996</th>\n", "      <td>799996</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>- 1118843881 2005.06.15 R21-M0-N4-C:J03-U11 20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>799997</th>\n", "      <td>799997</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>- 1118843881 2005.06.15 R21-M0-N4-C:J15-U01 20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>799998</th>\n", "      <td>799998</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>- 1118843881 2005.06.15 R21-M0-N4-C:J17-U01 20...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>799999</th>\n", "      <td>799999</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>- 1118843881 2005.06.15 R21-M0-N4-C:J11-U01 20...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>800000 rows × 4 columns</p>\n", "</div>"], "text/plain": ["        LineID  Label  OOV_weight  \\\n", "0            0      0           0   \n", "1            1      0           0   \n", "2            2      0           0   \n", "3            3      0           0   \n", "4            4      0           0   \n", "...        ...    ...         ...   \n", "799995  799995      0           0   \n", "799996  799996      0           0   \n", "799997  799997      0           0   \n", "799998  799998      0           0   \n", "799999  799999      0           0   \n", "\n", "                                                  Content  \n", "0       - 1117838570 2005.06.03 R02-M1-N0-C:J12-U11 20...  \n", "1       - 1117838570 2005.06.03 R02-M1-N0-C:J12-U11 20...  \n", "2       - 1117838570 2005.06.03 R02-M1-N0-C:J12-U11 20...  \n", "3       - 1117838570 2005.06.03 R02-M1-N0-C:J12-U11 20...  \n", "4       - 1117838570 2005.06.03 R02-M1-N0-C:J12-U11 20...  \n", "...                                                   ...  \n", "799995  - 1118843881 2005.06.15 R21-M0-N4-C:J03-U01 20...  \n", "799996  - 1118843881 2005.06.15 R21-M0-N4-C:J03-U11 20...  \n", "799997  - 1118843881 2005.06.15 R21-M0-N4-C:J15-U01 20...  \n", "799998  - 1118843881 2005.06.15 R21-M0-N4-C:J17-U01 20...  \n", "799999  - 1118843881 2005.06.15 R21-M0-N4-C:J11-U01 20...  \n", "\n", "[800000 rows x 4 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df_train"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>LineID</th>\n", "      <th>Label</th>\n", "      <th>Content</th>\n", "      <th>OOV_weight</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>800000</td>\n", "      <td>0</td>\n", "      <td>- 1118843881 2005.06.15 R21-M0-N4-C:J07-U01 20...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>800001</td>\n", "      <td>0</td>\n", "      <td>- 1118843881 2005.06.15 R21-M0-N4-C:J13-U01 20...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>800002</td>\n", "      <td>0</td>\n", "      <td>- 1118843881 2005.06.15 R21-M0-N4-C:J09-U01 20...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>800003</td>\n", "      <td>0</td>\n", "      <td>- 1118843881 2005.06.15 R21-M0-N4-C:J14-U11 20...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>800004</td>\n", "      <td>0</td>\n", "      <td>- 1118843881 2005.06.15 R21-M0-N4-C:J10-U11 20...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>199995</th>\n", "      <td>999995</td>\n", "      <td>0</td>\n", "      <td>- 1119725231 2005.06.25 R37-M1-N3-C:J05-U11 20...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>199996</th>\n", "      <td>999996</td>\n", "      <td>0</td>\n", "      <td>- 1119725231 2005.06.25 R37-M1-N3-C:J03-U11 20...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>199997</th>\n", "      <td>999997</td>\n", "      <td>0</td>\n", "      <td>- 1119725231 2005.06.25 R37-M1-N3-C:J03-U11 20...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>199998</th>\n", "      <td>999998</td>\n", "      <td>0</td>\n", "      <td>- 1119725231 2005.06.25 R37-M1-N3-C:J07-U11 20...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>199999</th>\n", "      <td>999999</td>\n", "      <td>0</td>\n", "      <td>- 1119725231 2005.06.25 R37-M1-N3-C:J07-U11 20...</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>200000 rows × 4 columns</p>\n", "</div>"], "text/plain": ["        LineID  Label                                            Content  \\\n", "0       800000      0  - 1118843881 2005.06.15 R21-M0-N4-C:J07-U01 20...   \n", "1       800001      0  - 1118843881 2005.06.15 R21-M0-N4-C:J13-U01 20...   \n", "2       800002      0  - 1118843881 2005.06.15 R21-M0-N4-C:J09-U01 20...   \n", "3       800003      0  - 1118843881 2005.06.15 R21-M0-N4-C:J14-U11 20...   \n", "4       800004      0  - 1118843881 2005.06.15 R21-M0-N4-C:J10-U11 20...   \n", "...        ...    ...                                                ...   \n", "199995  999995      0  - 1119725231 2005.06.25 R37-M1-N3-C:J05-U11 20...   \n", "199996  999996      0  - 1119725231 2005.06.25 R37-M1-N3-C:J03-U11 20...   \n", "199997  999997      0  - 1119725231 2005.06.25 R37-M1-N3-C:J03-U11 20...   \n", "199998  999998      0  - 1119725231 2005.06.25 R37-M1-N3-C:J07-U11 20...   \n", "199999  999999      0  - 1119725231 2005.06.25 R37-M1-N3-C:J07-U11 20...   \n", "\n", "        OOV_weight  \n", "0                0  \n", "1                0  \n", "2                0  \n", "3                0  \n", "4                0  \n", "...            ...  \n", "199995           0  \n", "199996           0  \n", "199997           0  \n", "199998           0  \n", "199999           0  \n", "\n", "[200000 rows x 4 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df_test"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    578479\n", "1    221521\n", "Name: Label, dtype: int64"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df_train['Label'].value_counts()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    196600\n", "1      3400\n", "Name: Label, dtype: int64"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df_test['Label'].value_counts()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 0,  1,  4,  7,  2,  5, 36,  8,  6,  9,  3])"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df_test['OOV_weight'].unique()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_22821/605570204.py:1: UserWarning: Boolean Series key will be reindexed to match DataFrame index.\n", "  df_test[df_rested['OOV_weight'] > 0].count()\n"]}, {"data": {"text/plain": ["LineID        208\n", "Label         208\n", "Content       208\n", "OOV_weight    208\n", "dtype: int64"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df_test[df_rested['OOV_weight'] > 0].count()"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>LineID</th>\n", "      <th>Label</th>\n", "      <th>Content</th>\n", "      <th>OOV_weight</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1000000</td>\n", "      <td>0</td>\n", "      <td>- 1119725231 2005.06.25 R37-M1-N3-C:J15-U01 20...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1000001</td>\n", "      <td>0</td>\n", "      <td>- 1119725231 2005.06.25 R37-M1-N3-C:J15-U01 20...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1000002</td>\n", "      <td>0</td>\n", "      <td>- 1119725231 2005.06.25 R37-M1-N3-C:J17-U01 20...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1000003</td>\n", "      <td>0</td>\n", "      <td>- 1119725231 2005.06.25 R37-M1-N3-C:J17-U01 20...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1000004</td>\n", "      <td>0</td>\n", "      <td>- 1119725231 2005.06.25 R37-M1-N3-C:J11-U01 20...</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3747958</th>\n", "      <td>4747958</td>\n", "      <td>1</td>\n", "      <td>KERNSOCK 1136390405 2006.01.04 R00-M0-NC-I:J18...</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3747959</th>\n", "      <td>4747959</td>\n", "      <td>1</td>\n", "      <td>KERNSOCK 1136390405 2006.01.04 R36-M0-NC-I:J18...</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3747960</th>\n", "      <td>4747960</td>\n", "      <td>1</td>\n", "      <td>KERNSOCK 1136390405 2006.01.04 R30-M0-NC-I:J18...</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3747961</th>\n", "      <td>4747961</td>\n", "      <td>1</td>\n", "      <td>KERNSOCK 1136390405 2006.01.04 R31-M0-NC-I:J18...</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3747962</th>\n", "      <td>4747962</td>\n", "      <td>1</td>\n", "      <td>KERNSOCK 1136390405 2006.01.04 R34-M0-NC-I:J18...</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3747963 rows × 4 columns</p>\n", "</div>"], "text/plain": ["          LineID  Label                                            Content  \\\n", "0        1000000      0  - 1119725231 2005.06.25 R37-M1-N3-C:J15-U01 20...   \n", "1        1000001      0  - 1119725231 2005.06.25 R37-M1-N3-C:J15-U01 20...   \n", "2        1000002      0  - 1119725231 2005.06.25 R37-M1-N3-C:J17-U01 20...   \n", "3        1000003      0  - 1119725231 2005.06.25 R37-M1-N3-C:J17-U01 20...   \n", "4        1000004      0  - 1119725231 2005.06.25 R37-M1-N3-C:J11-U01 20...   \n", "...          ...    ...                                                ...   \n", "3747958  4747958      1  KERNSOCK 1136390405 2006.01.04 R00-M0-NC-I:J18...   \n", "3747959  4747959      1  KERNSOCK 1136390405 2006.01.04 R36-M0-NC-I:J18...   \n", "3747960  4747960      1  KERNSOCK 1136390405 2006.01.04 R30-M0-NC-I:J18...   \n", "3747961  4747961      1  KERNSOCK 1136390405 2006.01.04 R31-M0-NC-I:J18...   \n", "3747962  4747962      1  KERNSOCK 1136390405 2006.01.04 R34-M0-NC-I:J18...   \n", "\n", "         OOV_weight  \n", "0                 0  \n", "1                 0  \n", "2                 0  \n", "3                 0  \n", "4                 0  \n", "...             ...  \n", "3747958           5  \n", "3747959           5  \n", "3747960           5  \n", "3747961           5  \n", "3747962           5  \n", "\n", "[3747963 rows x 4 columns]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df_rested"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 0,  5,  7,  8,  1, 36, 40, 41,  3,  2,  6,  4, 42,  9, 10, 13, 11,\n", "       37, 20, 39, 19, 12, 15, 16, 14, 30, 24, 29, 34, 44, 49, 54, 59, 64])"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["df_rested['OOV_weight'].unique()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["selected_rows = df_rested[df_rested['OOV_weight'] > 0]"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>LineID</th>\n", "      <th>Label</th>\n", "      <th>Content</th>\n", "      <th>OOV_weight</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>7352</th>\n", "      <td>1007352</td>\n", "      <td>0</td>\n", "      <td>- 1119725459 2005.06.25 - 2005-06-25-11.50.59....</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25504</th>\n", "      <td>1025504</td>\n", "      <td>0</td>\n", "      <td>- 1119754873 2005.06.25 - 2005-06-25-20.01.13....</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25554</th>\n", "      <td>1025554</td>\n", "      <td>0</td>\n", "      <td>- 1119758939 2005.06.25 - 2005-06-25-21.08.59....</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25769</th>\n", "      <td>1025769</td>\n", "      <td>0</td>\n", "      <td>- 1119773768 2005.06.26 R01-M1-ND-C:J15-U11 20...</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25773</th>\n", "      <td>1025773</td>\n", "      <td>0</td>\n", "      <td>- 1119797461 2005.06.26 R26-M0-NB-C:J07-U01 20...</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3747958</th>\n", "      <td>4747958</td>\n", "      <td>1</td>\n", "      <td>KERNSOCK 1136390405 2006.01.04 R00-M0-NC-I:J18...</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3747959</th>\n", "      <td>4747959</td>\n", "      <td>1</td>\n", "      <td>KERNSOCK 1136390405 2006.01.04 R36-M0-NC-I:J18...</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3747960</th>\n", "      <td>4747960</td>\n", "      <td>1</td>\n", "      <td>KERNSOCK 1136390405 2006.01.04 R30-M0-NC-I:J18...</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3747961</th>\n", "      <td>4747961</td>\n", "      <td>1</td>\n", "      <td>KERNSOCK 1136390405 2006.01.04 R31-M0-NC-I:J18...</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3747962</th>\n", "      <td>4747962</td>\n", "      <td>1</td>\n", "      <td>KERNSOCK 1136390405 2006.01.04 R34-M0-NC-I:J18...</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1256104 rows × 4 columns</p>\n", "</div>"], "text/plain": ["          LineID  Label                                            Content  \\\n", "7352     1007352      0  - 1119725459 2005.06.25 - 2005-06-25-11.50.59....   \n", "25504    1025504      0  - 1119754873 2005.06.25 - 2005-06-25-20.01.13....   \n", "25554    1025554      0  - 1119758939 2005.06.25 - 2005-06-25-21.08.59....   \n", "25769    1025769      0  - 1119773768 2005.06.26 R01-M1-ND-C:J15-U11 20...   \n", "25773    1025773      0  - 1119797461 2005.06.26 R26-M0-NB-C:J07-U01 20...   \n", "...          ...    ...                                                ...   \n", "3747958  4747958      1  KERNSOCK 1136390405 2006.01.04 R00-M0-NC-I:J18...   \n", "3747959  4747959      1  KERNSOCK 1136390405 2006.01.04 R36-M0-NC-I:J18...   \n", "3747960  4747960      1  KERNSOCK 1136390405 2006.01.04 R30-M0-NC-I:J18...   \n", "3747961  4747961      1  KERNSOCK 1136390405 2006.01.04 R31-M0-NC-I:J18...   \n", "3747962  4747962      1  KERNSOCK 1136390405 2006.01.04 R34-M0-NC-I:J18...   \n", "\n", "         OOV_weight  \n", "7352              5  \n", "25504             5  \n", "25554             5  \n", "25769             7  \n", "25773             7  \n", "...             ...  \n", "3747958           5  \n", "3747959           5  \n", "3747960           5  \n", "3747961           5  \n", "3747962           5  \n", "\n", "[1256104 rows x 4 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["selected_rows"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# oov_rate = [0.0001, 0.001, 0.01, 0.1]\n", "oov_rate = 0.001"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    179\n", "1     21\n", "Name: Label, dtype: int64"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["oov_samples = df_rested[df_rested['OOV_weight'] > 0].sample(n=int(oov_rate * df_test.shape[0]), random_state=42)\n", "class_count = oov_samples['Label'].value_counts()\n", "class_count"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>LineID</th>\n", "      <th>Label</th>\n", "      <th>Content</th>\n", "      <th>OOV_weight</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2101769</th>\n", "      <td>3101769</td>\n", "      <td>0</td>\n", "      <td>- 1124798247 2005.08.23 R76-M0-N4-I:J18-U01 20...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3408413</th>\n", "      <td>4408413</td>\n", "      <td>0</td>\n", "      <td>- 1133448401 2005.12.01 R76-M0-NF-C:J05-U11 20...</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3380759</th>\n", "      <td>4380759</td>\n", "      <td>0</td>\n", "      <td>- 1133448152 2005.12.01 R36-M1-N9-C:J05-U11 20...</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3009261</th>\n", "      <td>4009261</td>\n", "      <td>0</td>\n", "      <td>- 1131141902 2005.11.04 R62-M0-N1-C:J05-U11 20...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2800982</th>\n", "      <td>3800982</td>\n", "      <td>0</td>\n", "      <td>- 1131060281 2005.11.03 R66-M0-ND-C:J10-U01 20...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2293170</th>\n", "      <td>3293170</td>\n", "      <td>0</td>\n", "      <td>- 1125224685 2005.08.28 R40-M0-N0-C:J14-U11 20...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3206101</th>\n", "      <td>4206101</td>\n", "      <td>0</td>\n", "      <td>- 1132072791 2005.11.15 UNKNOWN_LOCATION 2005-...</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2817694</th>\n", "      <td>3817694</td>\n", "      <td>0</td>\n", "      <td>- 1131060617 2005.11.03 R67-M1-N1-C:J04-U01 20...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2172452</th>\n", "      <td>3172452</td>\n", "      <td>0</td>\n", "      <td>- 1125084344 2005.08.26 R66-M1-N2-C:J06-U11 20...</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2229219</th>\n", "      <td>3229219</td>\n", "      <td>0</td>\n", "      <td>- 1125220541 2005.08.28 R73-M1-N7-C:J06-U11 20...</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>200 rows × 4 columns</p>\n", "</div>"], "text/plain": ["          LineID  Label                                            Content  \\\n", "2101769  3101769      0  - 1124798247 2005.08.23 R76-M0-N4-I:J18-U01 20...   \n", "3408413  4408413      0  - 1133448401 2005.12.01 R76-M0-NF-C:J05-U11 20...   \n", "3380759  4380759      0  - 1133448152 2005.12.01 R36-M1-N9-C:J05-U11 20...   \n", "3009261  4009261      0  - 1131141902 2005.11.04 R62-M0-N1-C:J05-U11 20...   \n", "2800982  3800982      0  - 1131060281 2005.11.03 R66-M0-ND-C:J10-U01 20...   \n", "...          ...    ...                                                ...   \n", "2293170  3293170      0  - 1125224685 2005.08.28 R40-M0-N0-C:J14-U11 20...   \n", "3206101  4206101      0  - 1132072791 2005.11.15 UNKNOWN_LOCATION 2005-...   \n", "2817694  3817694      0  - 1131060617 2005.11.03 R67-M1-N1-C:J04-U01 20...   \n", "2172452  3172452      0  - 1125084344 2005.08.26 R66-M1-N2-C:J06-U11 20...   \n", "2229219  3229219      0  - 1125220541 2005.08.28 R73-M1-N7-C:J06-U11 20...   \n", "\n", "         OOV_weight  \n", "2101769           1  \n", "3408413          15  \n", "3380759           8  \n", "3009261           1  \n", "2800982           1  \n", "...             ...  \n", "2293170           1  \n", "3206101           7  \n", "2817694           1  \n", "2172452           1  \n", "2229219           1  \n", "\n", "[200 rows x 4 columns]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["oov_samples"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/plain": ["Int64Index([26188, 26654, 27999, 26329, 26376, 27776, 26398, 28074, 50486,\n", "            71056, 27426, 26448, 27512, 27343, 26738, 26844, 27034, 50073,\n", "            27716, 12044, 50488],\n", "           dtype='int64')"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["normal_indices_to_replace = df_test[df_test['Label'] == 0].sample(n=class_count[0], random_state=42).index\n", "abnormal_indices_to_replace = df_test[df_test['Label'] == 1].sample(n=class_count[1], random_state=42).index\n", "abnormal_indices_to_replace"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["df_test.loc[normal_indices_to_replace] = oov_samples[oov_samples[\"Label\"] == 0].values\n", "df_test.loc[abnormal_indices_to_replace] = oov_samples[oov_samples[\"Label\"] == 1].values\n"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["LineID                                                  4101505\n", "Label                                                         1\n", "Content       KERNTERM 1131483541 2005.11.08 R63-M0-N1-C:J02...\n", "OOV_weight                                                    3\n", "Name: 26188, dtype: object"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["df_test.iloc[26188]"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["# Write the DataFrame to a text file\n", "output_file = \"injected_test_{}.log\".format(oov_rate * 100)\n", "df_test['Content'].to_csv(output_file, sep='\\n', index=False, header=False)"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [], "source": ["df_train['Content'].to_csv(\"../data/raw_data/BGL/processed/train.log\", sep='\\n', index=False, header=False)"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    578479\n", "1    221521\n", "Name: Label, dtype: int64"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["df_train['Label'].value_counts()"]}], "metadata": {"kernelspec": {"display_name": "neural-log", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}