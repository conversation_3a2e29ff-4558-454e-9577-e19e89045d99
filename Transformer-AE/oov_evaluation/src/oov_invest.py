import numpy as np
import pandas as pd
import os
import logging
import re, string
import argparse
from collections import defaultdict
from tqdm import tqdm
import matplotlib.pyplot as plt

class OovProcessor(object):
    def __init__(self, train_ratio, test_ratio, num_used_lines=None, name='BGL') -> None:
        self.vocab = []
        self.num_used_lines = num_used_lines
        self.train_ratio = train_ratio
        self.test_ratio = test_ratio
        self.LineID = 0
        self.num_rested_lines = 0
        self.name = name
        logger.info(f"Initialized OovProcessor for {name} dataset with train_ratio={train_ratio}, num_used_lines={num_used_lines}")

    def clean(self, s):
        """ Preprocess log message
        Parameters
        ----------
        s: str, raw log message

        Returns
        -------
        str, preprocessed log message without number tokens and special characters
        """
        s = re.sub(r'(\d+\.){3}\d+(:\d+)?', " ", s)
        s = re.sub(r'(\/.*?\.[\S:]+)', ' ', s)
        s = re.sub('\]|\[|\)|\(|\=|\,|\;', ' ', s)
        s = re.sub(r'[/_]', ' ', s)
        s = " ".join([word.lower() if word.isupper() else word for word in s.strip().split()])
        s = re.sub('([A-Z][a-z]+)', r' \1', re.sub('([A-Z]+)', r' \1', s))
        s = " ".join([word for word in s.split() if not bool(re.search(r'\d', word))])
        trantab = str.maketrans(dict.fromkeys(list(string.punctuation)))
        content = s.translate(trantab)
        s = " ".join([word.lower().strip() for word in content.strip().split()])
        return s
    
    def split_files(self, full_file, output_path):
        # Check and create directory if it doesn't exist
        if not os.path.exists(output_path):
            os.makedirs(output_path)
            logger.info(f"Created directory: {output_path}")

        used_file = os.path.join(output_path, "used.log")
        rested_file = os.path.join(output_path, "rested.log")

        if self.name == "TDB":
            encoding = "iso-8859-1"
        else:
            encoding = "utf8"

        logger.info(f"Reading full file: {full_file} with encoding {encoding}")
        with open(full_file, 'r', encoding=encoding) as reader:
            lines = reader.readlines()
            total_lines = len(lines)
            logger.info(f"Total lines in file: {total_lines}")
            
            # If num_used_lines is None, use the entire dataset
            if self.num_used_lines is None:
                self.num_used_lines = total_lines
                self.num_rested_lines = 0
                
                # Write all lines to used file
                with open(used_file, 'w') as writer:
                    writer.writelines(lines)
                
                # Create an empty rested file
                with open(rested_file, 'w') as writer:
                    writer.write("")
                
                logger.info(f"Using entire dataset: {self.num_used_lines} lines")
            else:
                # Use specified number of lines
                if self.num_used_lines > total_lines:
                    logger.warning(f"num_used_lines ({self.num_used_lines}) is greater than total lines ({total_lines})")
                    self.num_used_lines = total_lines
                    self.num_rested_lines = 0
                else:
                    self.num_rested_lines = total_lines - self.num_used_lines

                used = lines[:self.num_used_lines]
                rested = lines[self.num_used_lines:]

                logger.info(f"Writing {len(used)} lines to {used_file}")
                with open(used_file, 'w') as writer:
                    writer.writelines(used)

                logger.info(f"Writing {len(rested)} lines to {rested_file}")
                with open(rested_file, 'w') as writer:
                    writer.writelines(rested)
                
                logger.info(f"Split dataset: {self.num_used_lines} used lines, {self.num_rested_lines} rested lines")
            

    def fit(self, in_file, out_file=None):
        logger.info(f"Loading used data from {in_file}")
        training_dict = defaultdict(list)
        testing_dict = defaultdict(list)
        LineID = 0

        # Check and create directory if it doesn't exist
        in_file_dir = os.path.dirname(in_file)
        if not os.path.exists(in_file_dir):
            os.makedirs(in_file_dir)
            logger.info(f"Created directory: {in_file_dir}")

        with open(in_file, "r") as reader:
            lines = reader.readlines()
            lines = [x.strip() for x in lines]
            logger.info(f"Loaded {len(lines)} lines from {in_file}")
        
        # train_size = int(self.train_ratio * len(lines))
        # train_set = lines[:train_size]
        # test_set = lines[train_size:]

        train_end_idx = int(len(lines) * self.train_ratio)
        test_end_idx = train_end_idx + int(len(lines) * self.test_ratio)
        train_set = lines[:train_end_idx]
        test_set = lines[train_end_idx:test_end_idx]

        logger.info(f"Split into {len(train_set)} training lines and {len(test_set)} testing lines")

        # Building vocabulary
        num_normal, num_abnormal = 0, 0
        for i, line in zip(tqdm(range(len(train_set)), desc="Building vocab..."), train_set):
            training_dict['LineID'].append(self.LineID)
            training_dict['Label'].append(1 if line[0] != "-" else 0)
            if line[0] == '-':
                num_normal += 1
            if line[0] != '-':
                num_abnormal += 1
            training_dict['OOV_weight'].append(0)
            training_dict['Content'].append(line)
            line = self.clean(line)
            tokens = line.split(" ")
            new_tokens = [token for token in tokens if token not in self.vocab]
            self.vocab.extend(new_tokens)
            self.LineID += 1
            

        logger.info(f"Training set: {num_normal} normal and {num_abnormal} abnormal")
        # Write vocab to file
        vocab_file = os.path.join(out_file, f"{self.name}_vocab.log")
        logger.info(f"Writing vocabulary ({len(self.vocab)} tokens) to {vocab_file}")
        with open(vocab_file, "w") as writer:
            vocab = " ".join(self.vocab)
            writer.write(vocab)

        # Process testing set
        num_normal, num_abnormal = 0, 0
        for i, line in zip(tqdm(range(len(test_set)), desc="Process testing set..."), test_set):
            oov_count = 0
            testing_dict['LineID'].append(self.LineID)
            testing_dict['Label'].append(1 if line[0] != "-" else 0)
            if line[0] == '-':
                num_normal += 1
            if line[0] != '-':
                num_abnormal += 1

            testing_dict['Content'].append(line)
            line = self.clean(line)
            tokens = line.split(" ")
            for token in tokens:
                if token not in self.vocab:
                    oov_count += 1
            testing_dict['OOV_weight'].append(oov_count)
            self.LineID += 1
            

        logger.info(f"Testing set: {num_normal} normal and {num_abnormal} abnormal")

        training_df = pd.DataFrame(training_dict)
        testing_df = pd.DataFrame(testing_dict)
        
        # Create output filenames based on parameters, including dataset name
        train_filename = f"{self.name}_training_ratio{self.train_ratio:.2f}.csv"
        test_filename = f"{self.name}_testing_ratio{self.train_ratio:.2f}.csv"
        
        train_path = os.path.join(out_file, train_filename)
        test_path = os.path.join(out_file, test_filename)
        
        logger.info(f"Saving training data ({len(training_df)} rows) to {train_path}")
        training_df.to_csv(train_path, index=False)
        
        logger.info(f"Saving testing data ({len(testing_df)} rows) to {test_path}")
        testing_df.to_csv(test_path, index=False)

        # Summarize OOV statistics for the testing data
        self.summarize_oov_statistics(testing_df, self.name)

    def transform(self, in_file, out_file=None):
        logger.info(f"Loading rested file from {in_file}")
        rested_dict = defaultdict(list)
        
        # Check if there are any rested lines to process
        if self.num_rested_lines == 0:
            logger.info("No rested lines to process")
            # Create an empty DataFrame
            rested_df = pd.DataFrame(columns=['LineID', 'Label', 'Content', 'OOV_weight'])
            # Save the empty DataFrame with a filename based on parameters, including dataset name
            rested_filename = f"{self.name}_rested_ratio{self.train_ratio:.2f}.csv"
            rested_path = os.path.join(out_file, rested_filename)
            logger.info(f"Saving empty rested data to {rested_path}")
            rested_df.to_csv(rested_path, index=False)
            return
        
        reader = open(in_file, "r")
        processbar = tqdm(total=self.num_rested_lines, desc="Process rested file...")
        i = 0
        while True:
            line = reader.readline()
            if not line:
                break
            line = line.strip()
            oov_count = 0
            rested_dict['LineID'].append(self.LineID)
            rested_dict['Label'].append(1 if line[0] != "-" else 0)
            rested_dict['Content'].append(line)
            line = self.clean(line)
            tokens = line.split(" ")
            for token in tokens:
                if token not in self.vocab:
                    oov_count += 1
            rested_dict['OOV_weight'].append(oov_count)
            self.LineID += 1
            i += 1
            processbar.update(1)
        
        processbar.close()
        rested_df = pd.DataFrame(rested_dict)
        
        # Create output filename based on parameters, including dataset name
        rested_filename = f"{self.name}_rested_ratio{self.train_ratio:.2f}.csv"
        rested_path = os.path.join(out_file, rested_filename)
        
        logger.info(f"Saving rested data ({len(rested_df)} rows) to {rested_path}")
        rested_df.to_csv(rested_path, index=False)

        self.summarize_oov_statistics(rested_df, self.name)

    def summarize_oov_statistics(self, test_df, name):
        """Summarize OOV statistics in the testing data
        
        Parameters
        ----------
        test_df: pandas.DataFrame, testing data with OOV_weight column
        name: str, dataset name for logging
        """
        # Summarize OOV lines in testing data
        oov_lines = test_df[test_df['OOV_weight'] > 0]
        total_lines = test_df.shape[0]
        oov_line_count = oov_lines.shape[0]
        oov_percentage = oov_line_count / total_lines * 100
        
        logger.info(f"--- OOV Statistics for {name} Testing Data ---")
        logger.info(f"Total number of lines: {total_lines}")
        logger.info(f"Number of OOV lines: {oov_line_count}")
        logger.info(f"Percentage of OOV lines: {oov_percentage:.2f}%")
        
        # Distribution of OOV weights
        oov_weight_counts = test_df['OOV_weight'].value_counts().sort_index()
        logger.info(f"Distribution of OOV weights:")
        for weight, count in oov_weight_counts.items():
            logger.info(f"  Weight {weight}: {count} lines")
        
        # OOV distribution by label
        oov_by_label = oov_lines['Label'].value_counts()
        normal_oov = oov_by_label.get(0, 0)
        abnormal_oov = oov_by_label.get(1, 0)
        logger.info(f"OOV lines by label:")
        logger.info(f"  Normal logs with OOV: {normal_oov}")
        logger.info(f"  Abnormal logs with OOV: {abnormal_oov}")
        logger.info("----------------------------------------")

def plot_oov_weight_distribution(weight_counts, dataset_name="Dataset", output_file="oov_weight_distribution.png"):
    """
    Plot the OOV weight distribution with a dynamic y-axis range.
    weight_counts: dict {weight: count}
    """
    # Define bins
    bins = [
        (1, 3, '1-3'),
        (4, 5, '3-5'),
        (6, 10, '5-10'),
        (11, float('inf'), '>10')
    ]

    # Count lines in each bin
    bin_counts = [0] * len(bins)
    for w, count in weight_counts.items():
        if 1 <= w <= 3:
            bin_counts[0] += count
        elif 4 <= w <= 5:
            bin_counts[1] += count
        elif 6 <= w <= 10:
            bin_counts[2] += count
        elif w > 10:
            bin_counts[3] += count

    max_val = max(bin_counts)
    use_broken_axis = max_val > 200

    # Axis limits
    lower_ylim = (0, min(100, max_val + 10))
    upper_ylim = (max(200, max_val * 0.9), max_val + max(10_000, int(max_val * 0.1)))

    bar_titles = [b[2] for b in bins]
    bar_colors = ['#7b8eea', '#a6e3a1', '#f7a6b9', '#7b8eea']

    if use_broken_axis:
        fig, (ax_upper, ax_lower) = plt.subplots(2, 1, sharex=True, figsize=(7, 6), gridspec_kw={'height_ratios': [2, 1]})

        bars_upper = ax_upper.bar(bar_titles, bin_counts, width=0.6, color=bar_colors, edgecolor='navy')
        bars_lower = ax_lower.bar(bar_titles, bin_counts, width=0.6, color=bar_colors, edgecolor='navy')

        ax_upper.set_ylim(upper_ylim)
        ax_lower.set_ylim(lower_ylim)

        # Red zigzag break
        d = .015
        kwargs = dict(transform=ax_upper.transAxes, color='red', clip_on=False, linewidth=2)
        ax_upper.plot((-d, +d), (-d, +d), **kwargs)
        ax_upper.plot((1 - d, 1 + d), (-d, +d), **kwargs)
        kwargs.update(transform=ax_lower.transAxes)
        ax_lower.plot((-d, +d), (1 - d, 1 + d), **kwargs)
        ax_lower.plot((1 - d, 1 + d), (1 - d, 1 + d), **kwargs)

        for idx, bar in enumerate(bars_upper):
            if bar.get_height() >= upper_ylim[0]:
                ax_upper.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 8000,
                              f'{bin_counts[idx]}', ha='center', va='bottom', fontsize=10)
        for idx, bar in enumerate(bars_lower):
            if bar.get_height() < upper_ylim[0]:
                ax_lower.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 10,
                              f'{bin_counts[idx]}', ha='center', va='bottom', fontsize=10)

        ax_upper.spines['bottom'].set_visible(False)
        ax_lower.spines['top'].set_visible(False)
        ax_upper.tick_params(labeltop=False)
        ax_lower.xaxis.tick_bottom()
        ax_upper.grid(axis='y', linestyle='--', alpha=0.7)
        ax_lower.grid(axis='y', linestyle='--', alpha=0.7)

        ax_lower.set_xlabel('Weight Range', fontsize=12)
        fig.text(0.04, 0.5, 'Number of OOV Lines', va='center', rotation='vertical', fontsize=12)

    else:
        fig, ax = plt.subplots(figsize=(7, 5))
        bars = ax.bar(bar_titles, bin_counts, width=0.6, color=bar_colors, edgecolor='navy')
        ax.set_ylim(0, max_val + 10)
        for idx, bar in enumerate(bars):
            ax.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 5,
                    f'{bin_counts[idx]}', ha='center', va='bottom', fontsize=10)
        ax.set_xlabel('Weight Range', fontsize=12)
        ax.set_ylabel('Number of OOV Lines', fontsize=12)
        ax.grid(axis='y', linestyle='--', alpha=0.7)

    fig.suptitle(f'Number of OOV Lines by Weight Range of {dataset_name} testing data', fontsize=14)
    fig.legend([plt.Rectangle((0, 0), 1, 1, color=c, ec='navy') for c in bar_colors], [b[2] for b in bins],
               loc='lower center', ncol=4, frameon=False, bbox_to_anchor=(0.5, 0.01))
    try:
        plt.tight_layout(rect=[0.05, 0.05, 1, 0.95])
    except:
        pass  # Ignore tight_layout errors
    plt.savefig(output_file)
    plt.show()

def aggregate_and_visualize_oov_stats(oov_stats, output_csv="oov_stats_summary.csv", output_fig="oov_stats_summary.png", weight_csv="oov_weight_counts.csv"):
    """
    oov_stats: list of dicts with keys: dataset, train_ratio, test_ratio, num_used_lines, total_lines, oov_lines, oov_percentage, oov_weight_counts
    """
    # Save summary table
    summary_cols = ["dataset", "train_ratio", "test_ratio", "num_used_lines", "total_lines", "oov_lines", "oov_percentage"]
    df = pd.DataFrame([{k: d[k] for k in summary_cols} for d in oov_stats])
    df.to_csv(output_csv, index=False)

    # Save weight counts table (long format: one row per setting per weight)
    weight_rows = []
    for d in oov_stats:
        for weight, count in d.get("oov_weight_counts", {}).items():
            weight_rows.append({
                "dataset": d["dataset"],
                "train_ratio": d["train_ratio"],
                "test_ratio": d["test_ratio"],
                "num_used_lines": d["num_used_lines"],
                "weight": weight,
                "count": count
            })
    if weight_rows:
        df_weight = pd.DataFrame(weight_rows)
        df_weight.to_csv(weight_csv, index=False)

    # Prepare for grouped bar chart
    # Each group: dataset, each bar: train-test setting
    group_col = "dataset"
    bar_col = "setting"
    value_col = "oov_lines"

    df["setting"] = df.apply(lambda row: f"{row['train_ratio']:.2f}-{row['test_ratio']:.2f}" + (f"\nN={row['num_used_lines']}" if row['num_used_lines'] is not None else ""), axis=1)
    datasets = df[group_col].unique()
    settings = df[bar_col].unique()

    x = np.arange(len(datasets))
    width = 0.8 / len(settings) if len(settings) > 1 else 0.5

    fig, ax = plt.subplots(figsize=(2.5 + 2*len(settings), 6))
    for idx, setting in enumerate(settings):
        vals = []
        for dataset in datasets:
            row = df[(df[group_col]==dataset) & (df[bar_col]==setting)]
            vals.append(row[value_col].values[0] if not row.empty else 0)
        ax.bar(x + idx*width, vals, width, label=setting)

    ax.set_xticks(x + width*(len(settings)-1)/2)
    ax.set_xticklabels(datasets)
    ax.set_ylabel("Total OOV Lines (OOV_weight > 0)")
    ax.set_xlabel("Dataset")
    ax.set_title("OOV Lines by Dataset and Train-Test Setting")
    ax.legend(title="Train-Test Setting", loc="best")
    plt.tight_layout()
    plt.savefig(output_fig)
    plt.show()

    # For each dataset/setting, also plot the OOV weight distribution as in visualization.py
    for d in oov_stats:
        dataset_name = d["dataset"]
        train_ratio = d["train_ratio"]
        test_ratio = d["test_ratio"]
        num_used_lines = d["num_used_lines"]
        weight_counts = d.get("oov_weight_counts", {})
        setting_str = f"{dataset_name}_train{train_ratio:.2f}_test{test_ratio:.2f}"
        if num_used_lines is not None:
            setting_str += f"_lines{num_used_lines}"
        output_file = f"oov_weight_distribution_{setting_str}.png"
        plot_oov_weight_distribution(weight_counts, dataset_name=setting_str, output_file=output_file)

    # Collect all unique settings and datasets
    # Now, settings are per-dataset, so we need to keep the mapping
    dataset_settings = {}
    for d in oov_stats:
        dataset = d["dataset"]
        setting_str = f"train{d['train_ratio']:.2f}_test{d['test_ratio']:.2f}"
        if d["num_used_lines"] is not None:
            setting_str += f"\nN={d['num_used_lines']}"
        if dataset not in dataset_settings:
            dataset_settings[dataset] = []
        if setting_str not in dataset_settings[dataset]:
            dataset_settings[dataset].append(setting_str)

    # Build sorted lists for grid: datasets (rows), settings (columns)
    datasets = list(dataset_settings.keys())
    # Get all unique settings across all datasets, sorted for consistent columns
    all_settings = sorted({setting for settings in dataset_settings.values() for setting in settings})
    nrows = len(datasets)
    ncols = len(all_settings)

    fig, axes = plt.subplots(nrows=nrows, ncols=ncols, figsize=(15*ncols, 15*nrows), squeeze=False)
    plt.subplots_adjust(left=0.12, right=0.98, top=0.92, bottom=0.10, hspace=0.6, wspace=0.25)  # <-- increase hspace

    # Define bins and labels
    bins = [
        (1, 3, '1-3'),
        (4, 5, '3-5'),
        (6, 10, '5-10'),
        (11, float('inf'), '>10')
    ]
    bar_titles = [b[2] for b in bins]
    bar_colors = ['#7b8eea', '#a6e3a1', '#f7a6b9', '#7b8eea']

    # Find global y-limits for all subplots
    all_bin_counts = []
    for d in oov_stats:
        weight_counts = d.get("oov_weight_counts", {})
        bin_counts = [0] * len(bins)
        for w, count in weight_counts.items():
            if 1 <= w <= 3:
                bin_counts[0] += count
            elif 4 <= w <= 5:
                bin_counts[1] += count
            elif 6 <= w <= 10:
                bin_counts[2] += count
            elif w > 10:
                bin_counts[3] += count
        all_bin_counts.extend(bin_counts)
    max_count = max(all_bin_counts) if all_bin_counts else 1
    lower_ylim = (0, 500)
    # Dynamically determine if we need broken y-axis based on max values
    needs_broken_axis = max_count > lower_ylim[1]
    if needs_broken_axis:
        # Calculate appropriate upper limit with some padding
        upper_ylim = (max(2000, lower_ylim[1] + 500), max_count + max(1000, max_count * 0.1))
    else:
        # Use single axis that can accommodate all values
        upper_ylim = (0, max(max_count + max(50, max_count * 0.1), lower_ylim[1] + 100))

    # Plot each subplot: rows are datasets, columns are settings
    for i, dataset in enumerate(datasets):
        for j, setting in enumerate(all_settings):
            ax_upper = axes[i, j]
            # Find the corresponding oov_stats entry
            d = next((x for x in oov_stats if x["dataset"] == dataset and (
                f"train{x['train_ratio']:.2f}_test{x['test_ratio']:.2f}" + (f"\nN={x['num_used_lines']}" if x['num_used_lines'] is not None else "")
            ) == setting), None)
            if d is None:
                ax_upper.axis('off')
                continue
            weight_counts = d.get("oov_weight_counts", {})
            bin_counts = [0] * len(bins)
            for w, count in weight_counts.items():
                if 1 <= w <= 3:
                    bin_counts[0] += count
                elif 4 <= w <= 5:
                    bin_counts[1] += count
                elif 6 <= w <= 10:
                    bin_counts[2] += count
                elif w > 10:
                    bin_counts[3] += count

            # Check if this specific subplot needs broken y-axis
            subplot_max = max(bin_counts) if bin_counts else 0
            subplot_needs_broken = subplot_max > lower_ylim[1] and needs_broken_axis

            if not subplot_needs_broken:
                # Single axis - use full range to accommodate values
                ax_upper.clear()
                bars = ax_upper.bar(bar_titles, bin_counts, width=0.6, color=bar_colors, edgecolor='navy', zorder=3)
                ax_upper.set_ylim(0, max(subplot_max + max(50, subplot_max * 0.1), lower_ylim[1] + 100))
                
                for idx, bar in enumerate(bars):
                    height = bar.get_height()
                    if height > 0:
                        ax_upper.text(bar.get_x() + bar.get_width()/2., height + max(5, height * 0.02),
                                      f'{bin_counts[idx]}', ha='center', va='bottom', fontsize=10)
                
                if i == nrows - 1:
                    ax_upper.set_xlabel('Weight Range', fontsize=12)
                if j == 0:
                    ax_upper.set_ylabel(dataset, fontsize=12)
                else:
                    ax_upper.set_ylabel('')
                if i == 0:
                    ax_upper.set_title(setting, fontsize=14, pad=20)
                
                # Set appropriate y-ticks based on the scale
                if subplot_max <= 1000:
                    tick_interval = 100
                elif subplot_max <= 10000:
                    tick_interval = 1000
                else:
                    tick_interval = 10000
                    
                y_max = ax_upper.get_ylim()[1]
                ax_upper.set_yticks(np.arange(0, y_max + 1, tick_interval))
                ax_upper.grid(axis='y', linestyle='--', alpha=0.7, zorder=0)
                
                if j != 0:
                    ax_upper.set_yticklabels([])
                if i != nrows - 1:
                    ax_upper.set_xticklabels([])
                continue

            # Broken y-axis case
            from mpl_toolkits.axes_grid1.inset_locator import inset_axes

            ax_upper.clear()
            pos = ax_upper.get_position()
            vertical_pad = 0.04
            upper_height = pos.height * 0.65 - vertical_pad/2
            lower_height = pos.height * 0.35 - vertical_pad/2
            ax_upper.set_position([pos.x0, pos.y0 + lower_height + vertical_pad, pos.width, upper_height])
            ax_lower = inset_axes(ax_upper,
                                  width="100%", height=f"{int(lower_height/pos.height*100)}%",
                                  bbox_to_anchor=(0, -lower_height/upper_height, 1, 1),
                                  bbox_transform=ax_upper.transAxes,
                                  borderpad=0, loc='lower left')

            bars_upper = ax_upper.bar(bar_titles, bin_counts, width=0.6, color=bar_colors, edgecolor='navy', zorder=3)
            bars_lower = ax_lower.bar(bar_titles, bin_counts, width=0.6, color=bar_colors, edgecolor='navy', zorder=3)

            ax_upper.set_ylim(upper_ylim)
            ax_lower.set_ylim(lower_ylim)

            ax_upper.spines['bottom'].set_visible(False)
            ax_lower.spines['top'].set_visible(False)
            ax_upper.tick_params(labeltop=False, bottom=False)
            ax_lower.xaxis.tick_bottom()

            d_ = .015
            kwargs = dict(transform=ax_upper.transAxes, color='red', clip_on=False, linewidth=2)
            ax_upper.plot((-d_, +d_), (-d_, +d_), **kwargs)
            ax_upper.plot((1 - d_, 1 + d_), (-d_, +d_), **kwargs)
            kwargs = dict(transform=ax_lower.transAxes, color='red', clip_on=False, linewidth=2)
            ax_lower.plot((-d_, +d_), (1 - d_, 1 + d), **kwargs)
            ax_lower.plot((1 - d_, 1 + d), (1 - d_, 1 + d), **kwargs)

            for idx, bar in enumerate(bars_upper):
                height = bar.get_height()
                if height >= upper_ylim[0]:
                    padding = max(5000, (upper_ylim[1] - upper_ylim[0]) * 0.02)
                    ax_upper.text(bar.get_x() + bar.get_width()/2., height + padding,
                                  f'{bin_counts[idx]}', ha='center', va='bottom', fontsize=10)
            for idx, bar in enumerate(bars_lower):
                height = bar.get_height()
                if height > 0 and height < upper_ylim[0]:
                    ax_lower.text(bar.get_x() + bar.get_width()/2., height + 30,
                                  f'{bin_counts[idx]}', ha='center', va='bottom', fontsize=10)

            # Axis labels and titles
            if i == nrows - 1:
                ax_lower.set_xlabel('Weight Range', fontsize=12)
            else:
                ax_lower.set_xlabel('')
            if j == 0:
                ax_upper.set_ylabel(dataset, fontsize=12)
            else:
                ax_upper.set_ylabel('')
            if i == 0:
                ax_upper.set_title(setting, fontsize=14, pad=20)
            if j == 0:
                ax_lower.set_ylabel('')
            if j != 0:
                ax_lower.set_yticklabels([])

            # Dynamic tick intervals for broken axis
            upper_range = upper_ylim[1] - upper_ylim[0]
            if upper_range <= 10000:
                upper_tick_interval = 1000
            elif upper_range <= 100000:
                upper_tick_interval = 10000
            else:
                upper_tick_interval = 100000
                
            ax_upper.set_yticks(np.arange(upper_ylim[0], upper_ylim[1]+1, upper_tick_interval))
            ax_lower.set_yticks(np.arange(lower_ylim[0], lower_ylim[1]+1, 100))

            ax_upper.grid(axis='y', linestyle='--', alpha=0.7, zorder=0)
            ax_lower.grid(axis='y', linestyle='--', alpha=0.7, zorder=0)

    legend_labels = [b[2] for b in bins]
    legend_handles = [
        plt.Rectangle((0, 0), 1, 1, color=bar_colors[i], ec='navy') for i in range(len(bar_colors))
    ]
    fig.legend(legend_handles, legend_labels, loc='lower center', ncol=4, frameon=False, bbox_to_anchor=(0.5, 0.01))

    plt.subplots_adjust(left=0.12, right=0.98, top=0.92, bottom=0.10, hspace=0.25, wspace=0.15)
    plt.savefig("oov_weight_distribution_grid.png", dpi=200)
    plt.show()

    
def parse_arguments():
    parser = argparse.ArgumentParser(description='Process log data for OOV analysis')
    parser.add_argument('--dataset', type=str, required=True,
                        help='Dataset name to process (e.g., BGL, SPIRIT, TDB)')
    parser.add_argument('--train_ratio', type=float, required=True,
                        help='Training ratio (e.g., 0.8)')
    parser.add_argument('--test_ratios', type=float, nargs='+', required=True,
                        help='List of testing ratios to process (e.g., 0.05 0.10 0.15 0.20)')
    parser.add_argument('--num_used_lines', type=int, default=None,
                        help='Number of lines to use (default: all)')
    parser.add_argument('--data_dir', type=str, default='../data',
                        help='Base directory for data (default: ../data)')
    parser.add_argument('--force_reprocess', action='store_true',
                        help='Force reprocessing even if cached data exists')
    return parser.parse_args()

if __name__ == "__main__":
    args = parse_arguments()
    
    # Prepare variables for single run
    name = args.dataset
    train_ratio = args.train_ratio
    test_ratios = args.test_ratios
    num_used_lines = args.num_used_lines

    # Create running-logs directory if it doesn't exist
    log_dir = "../running-logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    oov_stats = []
    prev_oov_lines = None
    for test_ratio in test_ratios:
        # Set up logging with file and console output
        log_filename = f"{log_dir}/{name}_train_ratio{train_ratio:.2f}_test_ratio{test_ratio:.2f}"
        if num_used_lines is not None:
            log_filename += f"_lines{num_used_lines}"
        log_filename += ".log"
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                            handlers=[
                logging.FileHandler(log_filename),
                logging.StreamHandler()
            ])
        logger = logging.getLogger(__name__)

        logger.info(f"Processing {name} data with train_ratio={train_ratio}, test_ratio={test_ratio}, num_used_lines={num_used_lines}")
        
        # Set up file paths
        data_dir = args.data_dir
        full_data_file = os.path.join(data_dir, f"{name}/{name}.log")
        
        # Create output directory with train ratio in the name
        output_dir = f"{name}_training_ratio{train_ratio:.2f}_testing_ratio{test_ratio:.2f}"
        if num_used_lines is not None:
            output_dir += f"_lines{num_used_lines}"
        
        outpath = os.path.join(data_dir, name, "processed", output_dir)
        
        # Create the processed directory if it doesn't exist
        processed_dir = os.path.join(data_dir, name, "processed")
        if not os.path.exists(processed_dir):
            os.makedirs(processed_dir)
            logger.info(f"Created directory: {processed_dir}")
        
        # Create the output directory if it doesn't exist
        if not os.path.exists(outpath):
            os.makedirs(outpath)
            logger.info(f"Created directory: {outpath}")
        
        # Check if processed data already exists
        train_file = os.path.join(outpath, f"{name}_training_ratio{train_ratio:.2f}.csv")
        test_file = os.path.join(outpath, f"{name}_testing_ratio{train_ratio:.2f}.csv")
        rested_file = os.path.join(outpath, f"{name}_rested_ratio{train_ratio:.2f}.csv")
        vocab_file = os.path.join(outpath, f"{name}_vocab.log")
        
        # Set up input/output files for raw processing
        infile = os.path.join(outpath, "used.log")
        rest_file = os.path.join(outpath, "rested.log")
        
        # Check if we can use cached data
        use_cached = (not args.force_reprocess and 
                    os.path.exists(train_file) and 
                    os.path.exists(test_file) and 
                    os.path.exists(rested_file) and
                    os.path.exists(vocab_file))
        
        if use_cached:
            logger.info(f"Found cached processed data in {outpath}")
            logger.info(f"Loading processed data for analysis")
            
            # Load the processed data
            train_df = pd.read_csv(train_file)
            test_df = pd.read_csv(test_file)
            rested_df = pd.read_csv(rested_file)
            
            # Load vocabulary
            with open(vocab_file, 'r') as f:
                vocab_text = f.read()
                vocab = vocab_text.split()
            
            logger.info(f"Loaded cached data: {len(train_df)} training rows, {len(test_df)} testing rows, {len(rested_df)} rested rows, {len(vocab)} vocabulary items")
            
            # Initialize processor with loaded vocabulary
            oov_processor = OovProcessor(train_ratio=train_ratio, test_ratio=test_ratio, num_used_lines=num_used_lines, name=name)
            oov_processor.vocab = vocab
            
            # Summarize OOV statistics
            logger.info(f"Summarizing OOV statistics from cached data")
            oov_processor.summarize_oov_statistics(test_df, name)
            if len(rested_df) > 0:
                oov_processor.summarize_oov_statistics(rested_df, f"{name} Rested")
            # Aggregate OOV stats for visualization
            oov_lines = (test_df['OOV_weight'] > 0).sum()
            total_lines = len(test_df)
            # Store OOV weight counts as dict {weight: count}
            oov_weight_counts = test_df['OOV_weight'].value_counts().sort_index().to_dict()
            oov_stats.append({
                "dataset": name,
                "train_ratio": train_ratio,
                "test_ratio": test_ratio,
                "num_used_lines": num_used_lines,
                "total_lines": total_lines,
                "oov_lines": oov_lines,
                "oov_percentage": 100.0 * oov_lines / total_lines if total_lines > 0 else 0,
                "oov_weight_counts": oov_weight_counts,
                "delta_oov": None  # Will be filled later
            })
        else:
            # Process the data from scratch
            logger.info(f"No cached data found or force_reprocess=True. Processing from raw data.")
            
            # Initialize processor and process data
            oov_processor = OovProcessor(train_ratio=train_ratio, test_ratio=test_ratio, num_used_lines=num_used_lines, name=name)
            
            # Process the data
            logger.info(f"Starting processing for {name} dataset")
            oov_processor.split_files(full_file=full_data_file, output_path=outpath)
            oov_processor.fit(infile, outpath)
            oov_processor.transform(rest_file, outpath)
            # Aggregate OOV stats for visualization
            test_file = os.path.join(outpath, f"{name}_testing_ratio{train_ratio:.2f}.csv")
            if os.path.exists(test_file):
                test_df = pd.read_csv(test_file)
                oov_lines = (test_df['OOV_weight'] > 0).sum()
                total_lines = len(test_df)
                oov_weight_counts = test_df['OOV_weight'].value_counts().sort_index().to_dict()
                oov_stats.append({
                    "dataset": name,
                    "train_ratio": train_ratio,
                    "test_ratio": test_ratio,
                    "num_used_lines": num_used_lines,
                    "total_lines": total_lines,
                    "oov_lines": oov_lines,
                    "oov_percentage": 100.0 * oov_lines / total_lines if total_lines > 0 else 0,
                    "oov_weight_counts": oov_weight_counts,
                    "delta_oov": None  # Will be filled later
                })
        logger.info(f"Processing complete. Results saved to {outpath}")

    # After all settings, aggregate and visualize
    if oov_stats:
        # Compute delta_oov for each test_ratio
        prev_oov = None
        for d in oov_stats:
            if prev_oov is None:
                d["delta_oov"] = ""
            else:
                d["delta_oov"] = str(d["oov_lines"] - prev_oov)
            prev_oov = d["oov_lines"]

        # Print summary table in requested format
        print("\nSummary Table:")
        header = [
            "Dataset", "Train-Test Ratio", "OOV Rate (Number of OOV lines)", "Delta OOV (compared to previous setting)",
            "OOV Weight", "", "", ""
        ]
        subheader = ["", "", "", "", "1-3", "3-5", "5-10", ">10"]
        print("{:<8} {:<15} {:<28} {:<32} {:<6} {:<6} {:<6} {:<6}".format(*header))
        print("{:<8} {:<15} {:<28} {:<32} {:<6} {:<6} {:<6} {:<6}".format(*subheader))

        def ratio_str(tr, te):
            return f"{int(tr*100)}%-{int(te*100)}%"

        for d in oov_stats:
            tr, te = d["train_ratio"], d["test_ratio"]
            ratio = ratio_str(tr, te)
            oov_lines = d["oov_lines"]
            delta_oov = d["delta_oov"]
            wc = d.get("oov_weight_counts", {})
            w_1_3 = sum([wc.get(i,0) for i in range(1,4)])
            w_3_5 = sum([wc.get(i,0) for i in range(4,6)])
            w_5_10 = sum([wc.get(i,0) for i in range(6,11)])
            w_10p = sum([wc.get(i,0) for i in wc if i > 10])
            print("{:<8} {:<15} {:<28} {:<32} {:<6} {:<6} {:<6} {:<6}".format(
                d["dataset"],
                ratio,
                str(oov_lines),
                delta_oov,
                str(w_1_3),
                str(w_3_5),
                str(w_5_10),
                str(w_10p)
            ))


# To run oov_invest.py in Ubuntu, use the following command in your terminal:
# (Make sure you are in the directory containing oov_invest.py or provide the full path.)

# Example: Run for a single dataset and setting
# python oov_invest.py --dataset BGL --train_ratio 0.8 --test_ratio 0.2 --num_used_lines 10000

# Example: Run for multiple datasets, ratio pairs, and num_used_lines
# python oov_invest.py --datasets BGL SPIRIT --ratios 0.8 0.2 0.7 0.3 --num_used_lines_list 10000 20000

# Add --force_reprocess if you want to ignore cached results and reprocess everything
# python oov_invest.py --dataset BGL --train_ratio 0.8 --test_ratio 0.2 --force_reprocess

# For help and all options
# python oov_invest.py --help
