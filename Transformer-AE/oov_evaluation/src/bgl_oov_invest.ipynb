{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "\n", "rested_data = \"../data/raw_data/BGL/processed/rested.csv\"\n", "train_data = \"../data/raw_data/BGL/processed/training.csv\"\n", "test_data = \"../data/raw_data/BGL/processed/testing.csv\"\n", "\n", "df_rested = pd.read_csv(rested_data)\n", "df_train = pd.read_csv(train_data)\n", "df_test = pd.read_csv(test_data)\n", "\n", "# Summarize OOV lines in testing data\n", "oov_lines_in_test = df_test[df_test['OOV_weight'] > 0]\n", "print(f\"Total number of lines in testing data: {df_test.shape[0]}\")\n", "print(f\"Number of OOV lines in testing data: {oov_lines_in_test.shape[0]}\")\n", "print(f\"Percentage of OOV lines: {oov_lines_in_test.shape[0]/df_test.shape[0]*100:.2f}%\")\n", "\n", "# Distribution of OOV weights\n", "print(\"\\nDistribution of OOV weights in testing data:\")\n", "oov_weight_counts = df_test['OOV_weight'].value_counts().sort_index()\n", "print(oov_weight_counts)\n", "\n", "# OOV distribution by label\n", "print(\"\\nOOV lines by label:\")\n", "oov_by_label = oov_lines_in_test['Label'].value_counts()\n", "print(oov_by_label)\n", "print(f\"Normal logs with OOV: {oov_by_label.get(0, 0)}\")\n", "print(f\"Abnormal logs with OOV: {oov_by_label.get(1, 0)}\")"]}], "metadata": {"kernelspec": {"display_name": "neural-log", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}