{"cells": [{"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [], "source": ["rested_data = \"../data/raw_data/TDB/processed/rested.csv\"\n", "train_data = \"../data/raw_data/TDB/processed/training.csv\"\n", "test_data = \"../data/raw_data/TDB/processed/testing.csv\"\n", "\n", "df_rested = pd.read_csv(rested_data)\n", "df_train = pd.read_csv(train_data)\n", "df_test = pd.read_csv(test_data)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    799638\n", "1       362\n", "Name: Label, dtype: int64"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["df_train['Label'].value_counts()"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    199966\n", "1        34\n", "Name: Label, dtype: int64"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["df_test['Label'].value_counts()"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([0, 1, 6])"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["df_test['OOV_weight'].unique()"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"data": {"text/plain": ["LineID        6\n", "Label         6\n", "Content       6\n", "OOV_weight    6\n", "dtype: int64"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["df_test[df_test['OOV_weight'] > 0].count()"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([ 0,  1,  7,  3, 10,  4,  6,  2,  5,  8, 57, 74,  9, 76, 17, 14, 13,\n", "       11, 15, 12])"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["df_rested['OOV_weight'].unique()"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    8995459\n", "1       4541\n", "Name: Label, dtype: int64"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["df_rested['Label'].value_counts()"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["selected_rows = df_rested[df_rested['OOV_weight'] > 0]"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["# oov_rate = [0.0001, 0.001, 0.01, 0.1]\n", "oov_rate = 0.01"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["if oov_rate == 0.1:\n", "    normal_oov_samples = df_rested[(df_rested['OOV_weight'] > 0) & (df_rested['Label'] == 0)].sample(n=19966, random_state=42)\n", "    abnormal_oov_samples = df_rested[(df_rested['OOV_weight'] > 0) & (df_rested['Label'] == 1)].sample(n=34, random_state=42)\n", "    class_count = normal_oov_samples['Label'].value_counts()\n", "    abnormal_indices_to_replace = df_test[df_test['Label'] == 1].sample(n=34, random_state=42).index\n", "    normal_indices_to_replace = df_test[df_test['Label'] == 0].sample(n=19966, random_state=42).index\n", "    \n", "    df_test.loc[normal_indices_to_replace] = normal_oov_samples.values\n", "    df_test.loc[abnormal_indices_to_replace] = abnormal_oov_samples.values\n", "\n", "else:\n", "    oov_samples = df_rested[df_rested['OOV_weight'] > 0].sample(n=int(oov_rate * df_test.shape[0]), random_state=42)\n", "    class_count = oov_samples['Label'].value_counts()\n", "    normal_indices_to_replace = df_test[df_test['Label'] == 0].sample(n=class_count[0], random_state=42).index\n", "    abnormal_indices_to_replace = df_test[df_test['Label'] == 1].sample(n=class_count[1], random_state=42).index\n", "\n", "    df_test.loc[normal_indices_to_replace] = oov_samples[oov_samples[\"Label\"] == 0].values\n", "    df_test.loc[abnormal_indices_to_replace] = oov_samples[oov_samples[\"Label\"] == 1].values\n"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["# Write the DataFrame to a text file\n", "import csv\n", "output_file = \"../data/raw_data/TDB/processed/injected_test_{}.log\".format(oov_rate * 100)\n", "df_test['Content'].to_csv(output_file, sep='\\n', index=False, header=False, quoting=csv.QUOTE_NONE)"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["import csv\n", "df_train['Content'].to_csv(\"../data/raw_data/TDB/processed/train.log\", sep='\\n', index=False, header=False, quoting=csv.QUOTE_NONE)"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    799638\n", "1       362\n", "Name: Label, dtype: int64"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["df_train['Label'].value_counts()"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"data": {"text/plain": ["0    199966\n", "1        34\n", "Name: Label, dtype: int64"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["df_test['Label'].value_counts()"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training dataset has: 800000 samples where 799638 normal and 362 abnormal\n"]}], "source": ["with open(\"../data/raw_data/TDB/processed/train.log\", mode=\"r\", encoding='utf8') as f:\n", "    train_logs = f.readlines()\n", "\n", "train_num_normal, train_num_abnormal = 0, 0\n", "for train_log in train_logs:\n", "    if train_log[0] == \"-\":\n", "        train_num_normal += 1\n", "    if train_log[0] != \"-\": \n", "        train_num_abnormal += 1\n", "    \n", "print(\"Training dataset has: {} samples where {} normal and {} abnormal\".format(len(train_logs), train_num_normal, train_num_abnormal))"]}], "metadata": {"kernelspec": {"display_name": "neural-log", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}