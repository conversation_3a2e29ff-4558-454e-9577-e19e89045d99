import matplotlib.pyplot as plt
import numpy as np

# Data from log (weight: line count)
weight_counts = {
    1: 10,
    5: 2,
    6: 4,
    9: 2
}

# Define bins and labels (old setting)
bins = [
    (1, 3, '1-3'),
    (4, 5, '3-5'),
    (6, 10, '5-10'),
    (11, float('inf'), '>10')
]

# Count lines in each bin
bin_counts = [0] * len(bins)
for w, count in weight_counts.items():
    if 1 <= w <= 3:
        bin_counts[0] += count
    elif 4 <= w <= 5:
        bin_counts[1] += count
    elif 6 <= w <= 10:
        bin_counts[2] += count
    elif w > 10:
        bin_counts[3] += count

# Plot settings
bar_titles = [b[2] for b in bins]
bar_colors = ['#7b8eea', '#a6e3a1', '#f7a6b9', '#7b8eea']

fig, ax = plt.subplots(figsize=(7, 5.9), dpi=100)  # Explicitly set dpi for consistent sizing
bars = ax.bar(bar_titles, bin_counts, width=0.6, color=bar_colors, edgecolor='navy')

# Add value labels
for idx, bar in enumerate(bars):
    height = bar.get_height()
    ax.text(bar.get_x() + bar.get_width()/2., height + 0.2,
            f'{bin_counts[idx]}', ha='center', va='bottom', fontsize=10)

# Axis labels and title
ax.set_xlabel('Weight Range', fontsize=12)
ax.set_ylabel('Number of OOV Lines', fontsize=12)
ax.set_title('80% training 5% testing', fontsize=14)

# Y-ticks
ax.set_yticks(np.arange(0, max(bin_counts)+1, 2))
ax.set_ylim(0, 11)

# Grid lines
ax.grid(axis='y', linestyle='--', alpha=0.7)

# Legend
legend_labels = [b[2] for b in bins]
legend_handles = [
    plt.Rectangle((0, 0), 1, 1, color=bar_colors[i], ec='navy') for i in range(len(bar_colors))
]
ax.legend(legend_handles, legend_labels, loc='upper center', ncol=4, frameon=False, bbox_to_anchor=(0.5, -0.1))

plt.tight_layout(rect=[0.05, 0.0, 1, 0.95])  # Increase bottom margin (from 0.05 to 0.10)
plt.savefig("figures/oov_BGL_train0.8_test0.05.pdf")
plt.show()
