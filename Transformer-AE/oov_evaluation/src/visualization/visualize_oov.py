import matplotlib.pyplot as plt
import numpy as np

# Data setup
datasets = ['BGL', 'SPIRIT', 'TDB']
test_sizes = ['5%', '10%', '15%', '20%']
oov_buckets = ['1-3', '4-5', '6-10', '>10']
colors = ['#7b8eea', '#a6e3a1', '#f7a6b9', '#7b8eea']

data = {
    'BGL': [
        [10, 2, 6, 0],
        [35364, 100, 6, 50],
        [154680, 141, 96077, 54],
        [226297, 179, 138264, 54]
    ],
    'SPIRIT': [
        [227, 0, 1, 0],
        [1152, 0, 1, 0],
        [1264, 0, 1, 0],
        [1293, 0, 1, 0]
    ],
    'TDB': [
        [10, 7, 0, 0],
        [705, 7, 1, 0],
        [1636, 42, 12, 10],
        [2082, 90, 30, 31]
    ]
}

for dataset in datasets:
    counts = np.array(data[dataset])  # shape: (num_test_sizes, num_buckets)
    x = np.arange(len(test_sizes))
    width = 0.21

    fig, (ax, ax2) = plt.subplots(2, 1, sharex=True, figsize=(8, 6), dpi=100,
                                  gridspec_kw={'height_ratios': [1, 2]})
    # Choose break points for y-axis
    if dataset == 'BGL':
        lower, upper = 0, 200
        lower2, upper2 = 25000, np.max(counts) * 1.15
    elif dataset == 'TDB':
        lower, upper = 0, 100
        lower2, upper2 = 200, np.max(counts) * 1.15
    else:  # SPIRIT
        lower, upper = 0, 250
        lower2, upper2 = 650, np.max(counts) * 1.15

    for i, bucket in enumerate(oov_buckets):
        bar_pos = x + i*width - width*1.5
        bars1 = ax.bar(bar_pos, counts[:, i], width=width, label=bucket, color=colors[i], edgecolor='navy')
        bars2 = ax2.bar(bar_pos, counts[:, i], width=width, label=bucket, color=colors[i], edgecolor='navy')
        # Add value labels: always show the value of that bar close to the top of the bar
        for j in range(len(test_sizes)):
            height = counts[j, i]
            xpos = bar_pos[j]
            offset = max(0.01 * height, 1.0)
            if height < upper:
                # On lower axis
                ax2.text(xpos, height + offset, f'{height}', ha='center', va='bottom', fontsize=12)
            elif height > lower2:
                # On upper axis
                ax.text(xpos, height + offset, f'{height}', ha='center', va='bottom', fontsize=12)
            else:
                # Bar is in the gap: show value at the top of the lower axis in red
                ax2.text(xpos, upper - 1, f'{height}', ha='center', va='bottom', fontsize=12, color='red')

    # Set y-limits
    ax.set_ylim(lower2, upper2)
    ax2.set_ylim(lower, upper)

    # Hide the spines between ax and ax2
    ax.spines['bottom'].set_visible(False)
    ax2.spines['top'].set_visible(False)
    ax.xaxis.tick_top()
    ax.tick_params(labeltop=False)  # don't put tick labels at the top
    ax2.xaxis.tick_bottom()

    # Diagonal lines to indicate break
    d = .005  # size of diagonal lines (smaller for closer diagonals)
    gap = .001  # reduce white space between diagonals

    kwargs = dict(transform=ax.transAxes, color='k', clip_on=False)
    ax.plot((-d, +d), (-gap, +gap), **kwargs)        # top-left diagonal
    ax.plot((1 - d, 1 + d), (-gap, +gap), **kwargs)  # top-right diagonal

    kwargs.update(transform=ax2.transAxes)  # switch to the bottom axes
    ax2.plot((-d, +d), (1 - gap, 1 + gap), **kwargs)  # bottom-left diagonal
    ax2.plot((1 - d, 1 + d), (1 - gap, 1 + gap), **kwargs)  # bottom-right diagonal

    ax2.set_xlabel('Test Set Size', fontsize=18, fontweight='bold')
    ax2.set_ylabel('Number of OOV Lines', fontsize=18, fontweight='bold')
    ax.set_title(f'OOV Distribution for {dataset}', fontsize=20, fontweight='bold')
    ax2.set_xticks(x)
    ax2.set_xticklabels(test_sizes, fontsize=16, fontweight='bold')
    ax2.tick_params(axis='y', labelsize=16)
    ax.tick_params(axis='y', labelsize=16)
    ax2.grid(axis='y', linestyle='--', alpha=0.7)
    ax.grid(axis='y', linestyle='--', alpha=0.7)
    ax.legend(title='OOV Weight', loc='upper left', frameon=False, fontsize=14, title_fontsize=16)

    plt.tight_layout()
    plt.savefig(f"figures/oov_grouped_bar_{dataset}_broken.pdf")
    plt.show()
