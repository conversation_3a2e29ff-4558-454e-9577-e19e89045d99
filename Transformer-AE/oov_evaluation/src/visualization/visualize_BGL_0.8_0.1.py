import matplotlib.pyplot as plt
import numpy as np

# Data from log (weight: line count)
weight_counts = {
    1: 17894,
    2: 17429,
    3: 41,
    4: 98,
    5: 2,
    6: 3161,
    7: 8,
    9: 2,
    12: 8,
    13: 8,
    18: 7,
    19: 7,
    21: 4,
    24: 5,
    25: 5,
    30: 3,
    31: 3
}

# Define bins and labels
bins = [
    (1, 3, '1-3'),
    (4, 5, '3-5'),
    (6, 10, '5-10'),
    (11, float('inf'), '>10')
]

# Count lines in each bin
bin_counts = [0] * len(bins)
for w, count in weight_counts.items():
    if 1 <= w <= 3:
        bin_counts[0] += count
    elif 4 <= w <= 5:
        bin_counts[1] += count
    elif 6 <= w <= 10:
        bin_counts[2] += count
    elif w > 10:
        bin_counts[3] += count

# Plot settings
bar_titles = [b[2] for b in bins]
bar_colors = ['#7b8eea', '#a6e3a1', '#f7a6b9', '#7b8eea']

lower_ylim = (0, 4000)
upper_ylim = (20000, 50000)

fig, (ax_upper, ax_lower) = plt.subplots(
    2, 1, sharex=True, figsize=(7, 6),
    gridspec_kw={'height_ratios': [2, 1]}
)

# Bar plots for both axes
bars_upper = ax_upper.bar(bar_titles, bin_counts, width=0.6, color=bar_colors, edgecolor='navy')
bars_lower = ax_lower.bar(bar_titles, bin_counts, width=0.6, color=bar_colors, edgecolor='navy')

# Set y-limits
ax_upper.set_ylim(upper_ylim)
ax_lower.set_ylim(lower_ylim)

# Hide spines between plots
ax_upper.spines['bottom'].set_visible(False)
ax_lower.spines['top'].set_visible(False)
ax_upper.tick_params(labeltop=False)
ax_lower.xaxis.tick_bottom()

# Draw red zigzag marks
d = .015
kwargs = dict(transform=ax_upper.transAxes, color='red', clip_on=False, linewidth=2)
ax_upper.plot((-d, +d), (-d, +d), **kwargs)
ax_upper.plot((1 - d, 1 + d), (-d, +d), **kwargs)
kwargs = dict(transform=ax_lower.transAxes, color='red', clip_on=False, linewidth=2)
ax_lower.plot((-d, +d), (1 - d, 1 + d), **kwargs)
ax_lower.plot((1 - d, 1 + d), (1 - d, 1 + d), **kwargs)

# Add value labels (only in one plot per bar)
for idx, bar in enumerate(bars_upper):
    height = bar.get_height()
    if height >= upper_ylim[0]:
        ax_upper.text(bar.get_x() + bar.get_width()/2., height + 8000,
                      f'{bin_counts[idx]}', ha='center', va='bottom', fontsize=10)

for idx, bar in enumerate(bars_lower):
    height = bar.get_height()
    if height < upper_ylim[0]:
        ax_lower.text(bar.get_x() + bar.get_width()/2., height + 30,
                      f'{bin_counts[idx]}', ha='center', va='bottom', fontsize=10)

# Axis labels and title
ax_lower.set_xlabel('Weight Range', fontsize=12)
fig.suptitle('80% training and 10% testing', fontsize=14, x=0.55, y=0.91)  # Center title horizontally

# Shared y-axis label (centered)
fig.text(0.04, 0.5, 'Number of OOV Lines', va='center', rotation='vertical', fontsize=12)

# Y-ticks
ax_upper.set_yticks(np.arange(20000, 50000, 10000))
ax_lower.set_yticks(np.arange(0, 4000, 1000))

# Grid lines
ax_upper.grid(axis='y', linestyle='--', alpha=0.7)
ax_lower.grid(axis='y', linestyle='--', alpha=0.7)

# Legend
legend_labels = ['1-3', '3-5', '5-10', '>10']
legend_handles = [
    plt.Rectangle((0, 0), 1, 1, color=bar_colors[i], ec='navy') for i in range(len(bar_colors))
]
fig.legend(legend_handles, legend_labels, loc='lower center', ncol=4, frameon=False, bbox_to_anchor=(0.5, 0.01))

plt.tight_layout(rect=[0.05, 0.05, 1, 0.95])  # Leave space on left for shared ylabel
plt.savefig("figures/oov_BGL_train0.8_test0.1.pdf")
plt.show()
