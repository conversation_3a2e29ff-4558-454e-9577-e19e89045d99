import os
import sys
import gc
import datetime
sys.path.append("../../")
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2' 
# os.environ['CUDA_VISIBLE_DEVICES'] = ""
# 0 = all messages are logged (default behavior)
# 1 = INFO messages are not printed
# 2 = INFO and WARNING messages are not printed
# 3 = INFO, WARNING, and ERROR messages are not printed
import math
import pickle
import numpy as np
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras.utils import Sequence
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping
from tensorflow.keras.losses import SparseCategoricalCrossentropy, MeanSquaredError
from official.nlp import optimization
from tensorflow.keras.optimizers.schedules import ExponentialDecay

from sklearn.utils import shuffle

from neurallog.models import TransformerAEWithPureDecoder, TransformerAEWithResidualAttention
from neurallog import main_data_loader
from neurallog.utils import classification_report, occ_get_score

from sklearn.metrics import roc_curve, auc, precision_recall_fscore_support
from sklearn.svm import SVC
from sklearn.svm import OneClassSVM
from sklearn.neighbors import LocalOutlierFactor
from sklearn.ensemble import IsolationForest

import multiprocessing

np.random.seed(42)
tf.random.set_seed(42)

embed_dim = 768  # Embedding size for each token
max_len = 20
stride = 1

# No.1 is to use the decoder with only FC 
# No.2 is to use the decoder with cross attention and FC
# No.3 is to use the decoder with cross attention, residual encoder's output,\
#  masked attention and FC
exp_no = 1
model_name = 'AE'
# BGL; SPIRIT; TDB
dataset = 'BGL'
# 1m for 1 million logs; 10m for 10 million logs
name = "1m-2" #1m-2 is for 0.4 training ratio
log_file = f"../../logs/{dataset}/{name}_used.log"
# model_file = f'{dataset}_transformer_autoencoder.hdf5'

class BatchGenerator(Sequence):

    def __init__(self, X, Y, batch_size):
        self.X, self.Y = X, Y
        self.batch_size = batch_size

    def __len__(self):
        return int(np.ceil(len(self.X) / float(self.batch_size)))

    def __getitem__(self, idx):
        # print(self.batch_size)
        dummy = np.zeros(shape=(embed_dim,))
        x = self.X[idx * self.batch_size:min((idx + 1) * self.batch_size, len(self.X))] # len(X) == batch_size; x is a batch has index idx: each record contains window_size messages

        X = np.zeros((len(x), max_len, embed_dim))      # X is 

        Y = np.zeros((len(x), 2))
        item_count = 0     # index for sequence in batch

        # loop each sequence in batch
        # resize sequence and padding to original sequences have window_size to max_len
        for i in range(idx * self.batch_size, min((idx + 1) * self.batch_size, len(self.X))):
            x = self.X[i]
            if len(x) > max_len:
                x = x[-max_len:]
            # padding the first (max_len-len(x)) elements
            x = np.pad(np.array(x), pad_width=((max_len - len(x), 0), (0, 0)), mode='constant',
                       constant_values=0)
            X[item_count] = np.reshape(x, [max_len, embed_dim])
            # Y[item_count] = np.reshape(x, [max_len, embed_dim])
            item_count += 1

        return X[:], X[:]

def calculate_threshold(model, x, batch_size, percentile=99.7):
    """Calculate threshold based on training data reconstruction loss"""
    data_loader = BatchGenerator(x, x, batch_size)
    
    loss_list = []
    for batch_in in data_loader:
        outputs = model.predict(batch_in[0], verbose=0)
        loss = tf.keras.losses.mse(outputs, batch_in[0])
        loss_list.append(loss)
    
    loss_list = tf.concat(loss_list, axis=0)
    loss = tf.math.reduce_mean(tf.constant(loss_list), axis=1)
    
    # Calculate threshold at the specified percentile
    threshold = np.percentile(loss.numpy(), percentile)
    print(f"Calculated threshold at {percentile}th percentile: {threshold}")
    return threshold

def test_model(model, x, y, batch_size, threshold=None):
    x, y = shuffle(x, y)

    test_loader = BatchGenerator(x, y, batch_size)
    
    loss_list = []
    num = 0
    for batch_in in test_loader:
        num +=1 
        # print("Batch {} / {}".format(num, math.ceil(len(x)/batch_size)), end='')
        outputs = model.predict_generator(batch_in[0], steps=1)
        loss = tf.keras.losses.mse(outputs, batch_in[0])
        loss_list.append(loss)
    
    print(np.array(loss_list).shape)
    loss_list = tf.concat(loss_list, axis=0)
    print(loss_list.shape)
    loss = tf.math.reduce_mean(tf.constant(loss_list), axis=1)
    print(loss.shape)
    gc.collect()

    FPR_auto, TPR_auto, thresholds_auto = roc_curve(y, loss)
    auc_auto = auc(FPR_auto, TPR_auto)
    print("AUC - Transformer Autoencoder: {}".format(auc_auto))
    
    # Use provided threshold or calculate from test data if not provided
    if threshold is None:
        threshold = np.percentile(loss.numpy(), 99.7)
        print("Using test data threshold (99.7th percentile): {}".format(threshold))
    else:
        print("Using provided threshold from training data: {}".format(threshold))
    
    predictions = (loss >= threshold).numpy().astype(int)

    precision, recall, f1_score, _ = precision_recall_fscore_support(y, predictions, average='binary', pos_label=0)
    print("Precision: {}".format(precision))
    print("Recall: {}".format(recall))
    print("F1 Score: {}".format(f1_score))
    return auc_auto

def train_generator(training_generator, validate_generator, x_te, y_te, num_train_samples, num_val_samples, batch_size,
                    epoch_num, model_file=None, lr_rate=3e-4, cache=False):
    epochs = epoch_num
    steps_per_epoch = num_train_samples
    num_train_steps = steps_per_epoch * epochs
    num_warmup_steps = int(0.0001 * num_train_steps)

    init_lr = 3e-4
    optimizer = optimization.create_optimizer(init_lr=lr_rate,
                                              num_train_steps=num_train_steps,
                                              num_warmup_steps=num_warmup_steps,
                                              optimizer_type='adamw')
    
    
    def shrink_loss(y_true, y_pred):
        # Compute the shrink regularizer
        shrink_loss = tf.reduce_mean(tf.square(y_pred))
        return shrink_loss
    
    # return loss
    class LossHistory(keras.callbacks.Callback):
        @tf.function
        def on_train_begin(self, logs={}):
            self.losses = []
            self.val_losses = []

        @tf.function
        def on_epoch_end(self, epoch, logs={}):
            self.losses.append(logs.get('loss'))
            self.val_losses.append(logs.get('val_loss'))

    class EpochEvaluation(keras.callbacks.Callback):
        def __init__(self, model, X, Y, batch_size):
            super().__init__()
            self.model = model
            self.X = X
            self.Y = Y
            self.batch_size = batch_size
            self.auc = []

        @tf.function
        def on_epoch_end(self, epoch, logs=None):
            auc_auto = test_model(self.model, self.X, self.Y, self.batch_size)
            self.auc.append(auc_auto)
            
    if cache == True:
        model = keras.models.load_model(model_file, compile=False)
        print("Loaded saved model from {}".format(model_file))
    else:
        print("Training model from scratch")
        if exp_no == 1:
            model = TransformerAEWithPureDecoder(embed_dim=768, num_heads=12, hidden_dims=[512, 128], \
                                        num_layers=2, max_seq_length=max_len, latent_dim=64)
        elif exp_no == 2:
            model = TransformerAEWithResidualAttention(embed_dim=768, num_heads=12, hidden_dims=[256, 128], \
                                        num_layers=1, max_seq_length=max_len, latent_dim=64)
    
    model.compile(loss=[keras.losses.MeanSquaredError()], 
                loss_weights=[1.0],
                optimizer=optimizer)

    # checkpoint
    filepath = model_file
    checkpoint = ModelCheckpoint(filepath,
                                 monitor='val_loss',
                                 verbose=1,
                                 save_best_only=True,
                                 mode='min',
                                 save_format='h5')
    early_stop = EarlyStopping(
        monitor='val_loss', min_delta=0, patience=10, verbose=0, mode='auto',
        baseline=None, restore_best_weights=True
    )

    loss_save = LossHistory()
    auc_save = EpochEvaluation(model, x_te, y_te, 1024)

    callbacks_list = [checkpoint, early_stop]

    model.fit_generator(generator=training_generator,
                        steps_per_epoch=int(num_train_samples / batch_size),
                        epochs=epoch_num,
                        verbose=1,
                        validation_data=validate_generator,
                        validation_steps=int(num_val_samples / batch_size),
                        workers=16,
                        max_queue_size=32,
                        callbacks=callbacks_list,
                        shuffle=False
                        )
    
    # Calculate threshold from training data
    print("Calculating threshold from training data...")
    train_threshold = calculate_threshold(model, training_generator.X, batch_size)
    
    pickle.dump(auc_save.auc, open('../saved_data/{}_{}_epoch_auc.pkl'.format(dataset, exp_no), 'wb'))
    
    # Save threshold
    pickle.dump(train_threshold, open('../saved_data/{}_{}_threshold.pkl'.format(dataset, exp_no), 'wb'))

    return model, train_threshold

def train(X, Y, x_te, y_te, epoch_num, batch_size, model_file=None, model_type=None, lr_rate=3e-4, cache=False):
    X, Y = shuffle(X, Y)
    n_samples = len(X)

    train_x, train_y = X[:int(n_samples * 90 / 100)], Y[:int(n_samples * 90 / 100)]
    val_x, val_y = X[int(n_samples * 90 / 100):], Y[int(n_samples * 90 / 100):]

    training_generator, num_train_samples = BatchGenerator(train_x, train_x, batch_size), len(train_x)
    validate_generator, num_val_samples = BatchGenerator(val_x, val_x, batch_size), len(val_x)

    print("Number of training samples: {0} - Number of validating samples: {1}".format(num_train_samples,
                                                                                    num_val_samples))

    model, threshold = train_generator(training_generator, validate_generator, x_te, y_te, num_train_samples, num_val_samples, batch_size,
                            epoch_num, model_file=model_file, lr_rate=lr_rate, cache=cache)

    return model, threshold


def get_latent(model, x, batch_size):
    data_loader = BatchGenerator(x, x, batch_size=batch_size)

    inter_model = keras.Model(inputs=model.input, outputs=model.layers[5].output)
    print(inter_model.summary())
    latent = inter_model.predict_generator(data_loader, steps=math.ceil(len(x) / batch_size), verbose=1)

    return latent


def ocsvm_01_eval(latent_train, latent_test, y_test, occ_score): 
    latent_train = tf.math.reduce_mean(latent_train, axis=1)
    latent_test = tf.math.reduce_mean(latent_test, axis=1)

    oc_svm = OneClassSVM(nu=0.1)
    print("OCSVM-0.1 training --- ")
    oc_svm.fit(latent_train)
    del latent_train
    print("OCSVM-0.1 predicting --- ")

    concated_scores = oc_svm.decision_function(latent_test)

    FPR_ocsvm, TPR_ocsvm, thresholds_ocsvm = roc_curve(y_test, -concated_scores, pos_label=1)    # - is that: negative is for inliers
    auc_ocsvm = auc(FPR_ocsvm, TPR_ocsvm)
    print("AUC - OCSVM - 0.1: {}".format(auc_ocsvm))
    
    del latent_test
    del oc_svm
    del auc_ocsvm
    gc.collect()


def ocsvm_05_eval(latent_train, latent_test, y_test, occ_score): 
    latent_train = tf.math.reduce_mean(latent_train, axis=1)
    latent_test = tf.math.reduce_mean(latent_test, axis=1)

    oc_svm = OneClassSVM(nu=0.5)
    print("OCSVM-0.5 training --- ")
    oc_svm.fit(latent_train)
    del latent_train
    print("OCSVM-0.5 predicting --- ")

    concated_scores = oc_svm.decision_function(latent_test)
    FPR_ocsvm, TPR_ocsvm, thresholds_ocsvm = roc_curve(y_test, -concated_scores, pos_label=1)    # - is that: negative is for inliers
    auc_ocsvm = auc(FPR_ocsvm, TPR_ocsvm)
    print("AUC - OCSVM-0.5: {}".format(auc_ocsvm))
    
    del oc_svm
    del auc_ocsvm
    gc.collect()

def lof_eval(latent_train, latent_test, y_test, occ_score): 
    latent_train = tf.math.reduce_mean(latent_train, axis=1)
    latent_test = tf.math.reduce_mean(latent_test, axis=1)

    lof = LocalOutlierFactor(n_neighbors=20, novelty=True)
    print("LOF training --- ")
    lof.fit(latent_train)
    del latent_train
    print("LOF predicting --- ")

    concated_scores = lof.decision_function(latent_test)

    FPR_ocsvm, TPR_ocsvm, thresholds_ocsvm = roc_curve(y_test, -concated_scores, pos_label=1)    # - is that: negative is for inliers
    auc_ocsvm = auc(FPR_ocsvm, TPR_ocsvm)
    print("AUC - LOF: {}".format(auc_ocsvm))
    
    del lof
    del auc_ocsvm
    gc.collect()

def iso_eval(latent_train, latent_test, y_test, occ_score): 
    latent_train = tf.math.reduce_mean(latent_train, axis=1)
    latent_test = tf.math.reduce_mean(latent_test, axis=1)

    iso = IsolationForest(random_state=42)
    print("IF training --- ")
    iso.fit(latent_train)
    del latent_train
    print("IF predicting --- ")

    concated_scores = iso.decision_function(latent_test)
    FPR_ocsvm, TPR_ocsvm, thresholds_ocsvm = roc_curve(y_test, -concated_scores, pos_label=1)    # - is that: negative is for inliers
    auc_ocsvm = auc(FPR_ocsvm, TPR_ocsvm)
    print("AUC - ISOLATION FOREST: {}".format(auc_ocsvm))
    pool.close()
    del iso
    del auc_ocsvm
    gc.collect()

if __name__ == '__main__':
    tf.debugging.set_log_device_placement(False)
    
    (x_tr, y_tr), (x_te, y_te) = main_data_loader.load_supercomputers(
         log_file, train_ratio=0.8, windows_size=max_len,
         step_size=1, e_type='bert', mode='imbalance')

    pickle.dump(x_tr, open(f"../../logs/{dataset}/encoded_data/{name}_x_tr.pkl", "wb"))
    pickle.dump(y_tr, open(f"../../logs/{dataset}/encoded_data/{name}_y_tr.pkl", "wb"))
    pickle.dump(x_te, open(f"../../logs/{dataset}/encoded_data/{name}_x_te.pkl", "wb"))
    pickle.dump(y_te, open(f"../../logs/{dataset}/encoded_data/{name}_y_te.pkl", "wb"))
    
    # Load data from pickle files
    try:
        print("Loading data from pickle files...")
        x_tr = pickle.load(open(f"../../logs/{dataset}/encoded_data/{name}_x_tr.pkl", "rb"))
        y_tr = pickle.load(open(f"../../logs/{dataset}/encoded_data/{name}_y_tr.pkl", "rb"))
        x_te = pickle.load(open(f"../../logs/{dataset}/encoded_data/{name}_x_te.pkl", "rb"))
        y_te = pickle.load(open(f"../../logs/{dataset}/encoded_data/{name}_y_te.pkl", "rb"))
        print(f"Successfully loaded data: train samples={len(x_tr)}, test samples={len(x_te)}")
    except Exception as e:
        print(f"Error loading data from pickle: {e}")
        
    x_tr = [x_tr[i] for i in range(len(y_tr)) if y_tr[i] == 0 ]
    y_tr = [i for i in y_tr if i == 0]
    print('Nomal training set: {}'.format(len(x_tr)))

    cache = False
    saved_model_path = f"../saved_model/BGL/1_AE_20_1_BGL_1m-2_transformer.ckpt"
    threshold = None
    
    if cache:
        model = keras.models.load_model(saved_model_path, compile=False)
        try:
            threshold = pickle.load(open(f'../saved_data/{dataset}_{exp_no}_threshold.pkl', 'rb'))
            print(f"Loaded saved threshold: {threshold}")
        except:
            print("No saved threshold found, will calculate on test data")
    else: 
        model, threshold = train(x_tr, y_tr, x_te, y_te, epoch_num=1000, batch_size=64, 
                              model_file=f"../saved_model/{dataset}/{exp_no}_{model_name}_{max_len}_{stride}_{dataset}_{name}_transformer.ckpt", 
                              model_type='transformer', lr_rate=0.0003, cache=False)
        
    auc_auto = test_model(model, x_te, y_te, batch_size=1024, threshold=threshold)
