{"cells": [{"cell_type": "code", "execution_count": 5, "id": "babd91ae-5c13-4d74-96f1-ee1576cfa121", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-10-29 01:11:07.795449: E tensorflow/compiler/xla/stream_executor/cuda/cuda_dnn.cc:9342] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered\n", "2023-10-29 01:11:07.795488: E tensorflow/compiler/xla/stream_executor/cuda/cuda_fft.cc:609] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered\n", "2023-10-29 01:11:07.795515: E tensorflow/compiler/xla/stream_executor/cuda/cuda_blas.cc:1518] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered\n", "Loading ../../logs/BGL/bgl_1m\n", "/home/<USER>/.local/lib/python3.11/site-packages/transformers/tokenization_utils_base.py:1889: FutureWarning: The `use_auth_token` argument is deprecated and will be removed in v5 of Transformers.\n", "  warnings.warn(\n", "/home/<USER>/.local/lib/python3.11/site-packages/transformers/modeling_tf_utils.py:2652: FutureWarning: The `use_auth_token` argument is deprecated and will be removed in v5 of Transformers.\n", "  warnings.warn(\n", "Some weights of the PyTorch model were not used when initializing the TF 2.0 model TFBertModel: ['cls.predictions.transform.dense.weight', 'cls.predictions.transform.LayerNorm.weight', 'cls.predictions.transform.dense.bias', 'cls.predictions.bias', 'cls.seq_relationship.weight', 'cls.seq_relationship.bias', 'cls.predictions.transform.LayerNorm.bias']\n", "- This IS expected if you are initializing TFBertModel from a PyTorch model trained on another task or with another architecture (e.g. initializing a TFBertForSequenceClassification model from a BertForPreTraining model).\n", "- This IS NOT expected if you are initializing TFBertModel from a PyTorch model that you expect to be exactly identical (e.g. initializing a TFBertForSequenceClassification model from a BertForSequenceClassification model).\n", "All the weights of TFBertModel were initialized from the PyTorch model.\n", "If your task is similar to the task the model of the checkpoint was trained on, you can already use TFBertModel for predictions without further training.\n", "Loaded 1000000 lines!\n", "Dataset has: 1000000 samples where 775079 normal and 224921 abnormal\n", "Truncation was not explicitly activated but `max_length` is provided a specific value, please use `truncation=True` to explicitly truncate examples to max length. Defaulting to 'longest_first' truncation strategy. If you encode pairs of sequences (GLUE-style) with the tokenizer you can select this strategy more precisely by providing a specific strategy to `truncation`.\n", "Loading 99.37% - 155 unique logs\n", "last train index: 799950\n", "Loading 100.00\n", "Loading 100.12\n", "Loading 100.25\n", "Loading 100.38\n", "Loading 100.50\n", "Loading 100.62\n", "Loading 100.75\n", "Loading 100.88\n", "Loading 101.00\n", "Loading 101.12\n", "Loading 101.25\n", "Loading 101.38\n", "Loading 101.50\n", "Loading 101.62\n", "Loading 101.75\n", "Loading 101.88\n", "Loading 102.00\n", "Loading 102.12\n", "Loading 102.25\n", "Loading 102.38\n", "Loading 102.50\n", "Loading 102.62\n", "Loading 102.75\n", "Loading 102.88\n", "Loading 103.00\n", "Loading 103.12\n", "Loading 103.25\n", "Loading 103.38\n", "Loading 103.50\n", "Loading 103.62\n", "Loading 103.75\n", "Loading 103.88\n", "Loading 104.00\n", "Loading 104.12\n", "Loading 104.25\n", "Loading 104.38\n", "Loading 104.50\n", "Loading 104.62\n", "Loading 104.75\n", "Loading 104.88\n", "Loading 105.00\n", "Loading 105.12\n", "Loading 105.25\n", "Loading 105.38\n", "Loading 105.50\n", "Loading 105.62\n", "Loading 105.75\n", "Loading 105.88\n", "Loading 106.00\n", "Loading 106.12\n", "Loading 106.25\n", "Loading 106.38\n", "Loading 106.50\n", "Loading 106.62\n", "Loading 106.75\n", "Loading 106.88\n", "Loading 107.00\n", "Loading 107.12\n", "Loading 107.25\n", "Loading 107.38\n", "Loading 107.50\n", "Loading 107.62\n", "Loading 107.75\n", "Loading 107.88\n", "Loading 108.00\n", "Loading 108.12\n", "Loading 108.25\n", "Loading 108.38\n", "Loading 108.50\n", "Loading 108.62\n", "Loading 108.75\n", "Loading 108.88\n", "Loading 109.00\n", "Loading 109.12\n", "Loading 109.25\n", "Loading 109.38\n", "Loading 109.50\n", "Loading 109.62\n", "Loading 109.75\n", "Loading 109.88\n", "Loading 110.00\n", "Loading 110.12\n", "Loading 110.25\n", "Loading 110.38\n", "Loading 110.50\n", "Loading 110.62\n", "Loading 110.75\n", "Loading 110.88\n", "Loading 111.00\n", "Loading 111.12\n", "Loading 111.25\n", "Loading 111.38\n", "Loading 111.50\n", "Loading 111.62\n", "Loading 111.75\n", "Loading 111.88\n", "Loading 112.00\n", "Loading 112.12\n", "Loading 112.25\n", "Loading 112.38\n", "Loading 112.50\n", "Loading 112.62\n", "Loading 112.75\n", "Loading 112.88\n", "Loading 113.00\n", "Loading 113.12\n", "Loading 113.25\n", "Loading 113.38\n", "Loading 113.50\n", "Loading 113.62\n", "Loading 113.75\n", "Loading 113.88\n", "Loading 114.00\n", "Loading 114.12\n", "Loading 114.25\n", "Loading 114.38\n", "Loading 114.50\n", "Loading 114.62\n", "Loading 114.75\n", "Loading 114.88\n", "Loading 115.00\n", "Loading 115.12\n", "Loading 115.25\n", "Loading 115.38\n", "Loading 115.50\n", "Loading 115.62\n", "Loading 115.75\n", "Loading 115.88\n", "Loading 116.00\n", "Loading 116.12\n", "Loading 116.25\n", "Loading 116.38\n", "Loading 116.50\n", "Loading 116.62\n", "Loading 116.75\n", "Loading 116.88\n", "Loading 117.00\n", "Loading 117.12\n", "Loading 117.25\n", "Loading 117.38\n", "Loading 117.50\n", "Loading 117.62\n", "Loading 117.75\n", "Loading 117.88\n", "Loading 118.00\n", "Loading 118.12\n", "Loading 118.25\n", "Loading 118.38\n", "Loading 118.50\n", "Loading 118.62\n", "Loading 118.75\n", "Loading 118.88\n", "Loading 119.00\n", "Loading 119.12\n", "Loading 119.25\n", "Loading 119.38\n", "Loading 119.50\n", "Loading 119.62\n", "Loading 119.75\n", "Loading 119.88\n", "Loading 120.00\n", "Loading 120.12\n", "Loading 120.25\n", "Loading 120.38\n", "Loading 120.50\n", "Loading 120.62\n", "Loading 120.75\n", "Loading 120.88\n", "Loading 121.00\n", "Loading 121.12\n", "Loading 121.25\n", "Loading 121.38\n", "Loading 121.50\n", "Loading 121.62\n", "Loading 121.75\n", "Loading 121.88\n", "Loading 122.00\n", "Loading 122.12\n", "Loading 122.25\n", "Loading 122.38\n", "Loading 122.50\n", "Loading 122.62\n", "Loading 122.75\n", "Loading 122.88\n", "Loading 123.00\n", "Loading 123.12\n", "Loading 123.25\n", "Loading 123.38\n", "Loading 123.50\n", "Loading 123.62\n", "Loading 123.75\n", "Loading 123.88\n", "Loading 124.00\n", "Loading 124.12\n", "Loading 124.25\n", "Loading 124.38\n", "Loading 124.50\n", "Loading 124.62\n", "Loading 124.75\n", "Loading 124.88\n", "Total failure logs: 44991\n", "Training dataset has 800000 samples and 578479 normal samples and 221521 abnormal samples\n", "Testing dataset has 200000 samples and 196600 normal samples and 3400 abnormal samples\n", "Total: 199980 instances, 51356 anomaly, 148624 normal\n", "Train: 159990 instances, 50349 anomaly, 109641 normal\n", "Test: 39990 instances, 1007 anomaly, 38983 normal\n", "\n", "Nomal training set: 109641\n", "Number of training samples: 98676 - Number of validating samples: 10965\n", "Loaded saved model from ../saved_model/BGL/BGL/AE_50_5_BGL_1m_transformer.ckpt\n", "/tf/notebooks/Transformer-AE/demo/main_demo/Transformer_SAE_BGL.py:179: UserWarning: `Model.fit_generator` is deprecated and will be removed in a future version. Please use `Model.fit`, which supports generators.\n", "  model.fit_generator(generator=training_generator,\n", "Epoch 1/1000\n", " 6/96 [>.............................] - ETA: 54s - loss: 6.3146e-05 - dense_7_loss: 6.3146e-05 - dense_4_loss: 1.4883WARNING:tensorflow:Callback method `on_train_batch_end` is slow compared to the batch time (batch time: 0.2729s vs `on_train_batch_end` time: 0.2732s). Check your callbacks.\n", "WARNING:tensorflow:Callback method `on_train_batch_end` is slow compared to the batch time (batch time: 0.2729s vs `on_train_batch_end` time: 0.2732s). Check your callbacks.\n", "96/96 [==============================] - ETA: 0s - loss: 6.0981e-05 - dense_7_loss: 6.0981e-05 - dense_4_loss: 1.4863^C\n"]}], "source": ["!python Transformer_SAE_BGL.py"]}, {"cell_type": "code", "execution_count": null, "id": "01e4c545-beba-4ec9-ab1a-dcd896c7a30f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0rc1"}}, "nbformat": 4, "nbformat_minor": 5}