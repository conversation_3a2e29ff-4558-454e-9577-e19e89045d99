import os
import sys
import gc
import pickle
import math
import numpy as np
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras.utils import Sequence
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping
from official.nlp import optimization
from sklearn.utils import shuffle
from neurallog.models import TransformerAEWithPureDecoder, TransformerAEWithResidualAttention
from neurallog import main_data_loader
from neurallog.utils import classification_report, occ_get_score
from sklearn.metrics import roc_curve, auc, precision_recall_fscore_support
import numpy as np
import tensorflow as tf
from tensorflow.keras import layers, Model
from tensorflow.keras.initializers import GlorotUniform

np.random.seed(42)
tf.random.set_seed(42)

# Model Parameters
embed_dim = 768
max_len = 20
stride = 1
exp_no = 1
model_name = 'AE'
dataset = 'BGL'
name = "1m-2"
log_file = f"../../logs/{dataset}/{name}_used.log"

def get_angles(pos, i, d_model):
    angle_rates = 1 / np.power(10000, (2 * (i//2)) / np.float32(d_model))
    return pos * angle_rates

def positional_encoding(position, d_model):
    angle_rads = get_angles(np.arange(position)[:, np.newaxis],
                            np.arange(d_model)[np.newaxis, :],
                            d_model)

    # apply sin to even indices in the array; 2i
    angle_rads[:, 0::2] = np.sin(angle_rads[:, 0::2])

    # apply cos to odd indices in the array; 2i+1
    angle_rads[:, 1::2] = np.cos(angle_rads[:, 1::2])

    pos_encoding = angle_rads[np.newaxis, ...]

    return tf.cast(pos_encoding, dtype=tf.float32)

class PositionalEncoding(layers.Layer):
    def __init__(self, max_len, embed_dim):
        super(PositionalEncoding, self).__init__()
        self.pos_encoding = positional_encoding(max_len,
                                                embed_dim)

    @tf.function
    def call(self, x):
        seq_len = tf.shape(x)[1]
        x += self.pos_encoding[:, :seq_len, :]
        return x
    
class TransformerAEWithPureDecoder(Model):
    def __init__(self, embed_dim=768, num_heads=12, hidden_dims=[256, 128], latent_dim=64, \
                 num_layers=1, max_seq_length=512):
        super(TransformerAEWithPureDecoder, self).__init__()
        
        # Xavier initializer
        self.initializer = GlorotUniform(seed=42)

        # Input layer
        self.input_layer = layers.InputLayer(input_shape=(max_seq_length, embed_dim))
        
        # Positional encoding
        self.positional_encoding = PositionalEncoding(max_seq_length, embed_dim)
        
        # Transformer Encoder layers
        self.encoder_layers = [
            [
                layers.MultiHeadAttention(num_heads=num_heads, key_dim=embed_dim, \
                                          kernel_initializer=self.initializer, bias_initializer=self.initializer),
                layers.LayerNormalization(epsilon=1e-6),
                # layers.Dense(hidden_dim, activation='relu'),
                layers.Dense(embed_dim, activation='relu', \
                             kernel_initializer=self.initializer, bias_initializer=self.initializer),
                layers.LayerNormalization(epsilon=1e-6)
            ] for _ in range(num_layers)
        ]
        
        # Latent space layers
        self.fc_encoder_hidden_layers = [layers.Dense(hidden_dim, activation='relu', \
                                                      kernel_initializer=self.initializer, bias_initializer=self.initializer) \
                                 for hidden_dim in hidden_dims]

        self.fc_latent = layers.Dense(latent_dim, kernel_initializer=self.initializer, bias_initializer=self.initializer)

        self.fc_decoder_hidden_layers = [layers.Dense(hidden_dim, activation='relu', \
                                                      kernel_initializer=self.initializer, bias_initializer=self.initializer) \
                                    for hidden_dim in hidden_dims[::-1]]
        
        # self.fc_decoder_hidden_layers.append(layers.Dense(embed_dim))
   
        # Output layer
        self.fc_out = layers.Dense(embed_dim, kernel_initializer=self.initializer, bias_initializer=self.initializer)
        
    @tf.function
    def call(self, inputs):
        # Encoder part
        x = self.input_layer(inputs)
        x = self.positional_encoding(x)
        
        for mha, norm1, dense_ffn, norm2 in self.encoder_layers:
            attn_output = mha(x, x)
            x = norm1(x + attn_output)
            ffn_output = dense_ffn(x)
            x = norm2(x + ffn_output)
        
        encoder_output_memory = tf.identity(x)

        # Latent space
        for hidden_layer in self.fc_encoder_hidden_layers:
            x = hidden_layer(x)
        
        latent = self.fc_latent(x)
        x = tf.identity(latent)
        # Decoder part
        for hidden_layer in self.fc_decoder_hidden_layers:
            x = hidden_layer(x)

        # Output layer
        output = self.fc_out(x)
        return output
    
# ==========================
# Triplet Loss Function
# ==========================
def triplet_loss(anchor, positive, negative, margin=1.0):
    """
    Computes Triplet Loss: Ensures that normal samples cluster together, 
    and anomalies are pushed away in the latent space.
    """
    d_pos = tf.reduce_sum(tf.square(anchor - positive), axis=1)
    d_neg = tf.reduce_sum(tf.square(anchor - negative), axis=1)
    return tf.reduce_mean(tf.maximum(d_pos - d_neg + margin, 0.0))

# ==========================
# Dynamic Threshold Calculation
# ==========================
def compute_threshold(normal_loader, model):
    """
    Dynamically computes the anomaly threshold using the 3-sigma rule.
    """
    reconstruction_errors = []
    with tf.no_grad():
        for normal_data in normal_loader:
            reconstructed, _ = model.predict(normal_data)
            error = tf.reduce_mean(tf.square(normal_data - reconstructed), axis=1)
            reconstruction_errors.extend(error.numpy())

    mu = np.mean(reconstruction_errors)
    sigma = np.std(reconstruction_errors)
    return mu + 3 * sigma

# ==========================
# Batch Generator with Known Normal and Unknown Data
# ==========================
class BatchGenerator(Sequence):
    def __init__(self, known_normal_data, unknown_data, batch_size):
        self.known_normal_data = known_normal_data
        self.unknown_data = unknown_data
        self.batch_size = batch_size

    def __len__(self):
        return int(np.ceil(len(self.known_normal_data) / self.batch_size))

    def __getitem__(self, idx):
        batch_known_normal = self.known_normal_data[idx * self.batch_size:min((idx + 1) * self.batch_size, len(self.known_normal_data))]
        batch_unknown = self.unknown_data[idx * self.batch_size:min((idx + 1) * self.batch_size, len(self.unknown_data))]

        X_known_normal = np.zeros((len(batch_known_normal), max_len, embed_dim))
        X_unknown = np.zeros((len(batch_unknown), max_len, embed_dim))

        for i, x in enumerate(batch_known_normal):
            if len(x) > max_len:
                x = x[-max_len:]
            x = np.pad(x, ((max_len - len(x), 0), (0, 0)), mode='constant', constant_values=0)
            X_known_normal[i] = x

        for i, x in enumerate(batch_unknown):
            if len(x) > max_len:
                x = x[-max_len:]
            x = np.pad(x, ((max_len - len(x), 0), (0, 0)), mode='constant', constant_values=0)
            X_unknown[i] = x

        return [X_known_normal, X_unknown], X_known_normal  # Input and output are the same for reconstruction

# ==========================
# Training Function
# ==========================
def train_generator(known_normal_data, unknown_data, validate_generator, x_te, y_te, num_train_samples, num_val_samples, 
                    batch_size, epoch_num, model_file=None, lr_rate=3e-4):

    # Create optimizer
    num_train_steps = num_train_samples * epoch_num
    num_warmup_steps = int(0.0001 * num_train_steps)
    optimizer = optimization.create_optimizer(init_lr=lr_rate, num_train_steps=num_train_steps,
                                              num_warmup_steps=num_warmup_steps, optimizer_type='adamw')

    # Load Transformer Autoencoder
    print("Training model from scratch")
    if exp_no == 1:
        model = TransformerAEWithPureDecoder(embed_dim=embed_dim, num_heads=12, hidden_dims=[512, 128], 
                                             num_layers=2, max_seq_length=max_len, latent_dim=64)
    elif exp_no == 2:
        model = TransformerAEWithResidualAttention(embed_dim=embed_dim, num_heads=12, hidden_dims=[256, 128], 
                                                   num_layers=1, max_seq_length=max_len, latent_dim=64)

    # Compile Model with Custom Loss
    def custom_loss(y_true, y_pred):
        recon_loss = keras.losses.MeanSquaredError()(y_true, y_pred)
        return recon_loss  # Triplet loss will be added during training

    model.compile(loss=custom_loss, optimizer=optimizer)

    # Callbacks
    checkpoint = ModelCheckpoint(model_file, monitor='val_loss', verbose=1, save_best_only=True, mode='min')
    early_stop = EarlyStopping(monitor='val_loss', patience=10, verbose=1, restore_best_weights=True)
    callbacks_list = [checkpoint, early_stop]

    # Training Loop with Dynamic Threshold and Triplet Loss
    for epoch in range(epoch_num):
        threshold = compute_threshold(BatchGenerator(known_normal_data, unknown_data, batch_size), model)
        print(f"Epoch [{epoch+1}/{epoch_num}] - Updated Threshold: {threshold:.4f}")

        total_loss, total_recon_loss, total_triplet_loss = 0, 0, 0

        for batch in BatchGenerator(known_normal_data, unknown_data, batch_size):
            with tf.GradientTape() as tape:
                reconstructed = model(batch[0])  # Get reconstruction of known normal data
                
                # Compute Reconstruction Loss
                recon_loss = keras.losses.MeanSquaredError()(batch[0], reconstructed)

                # Select positives and negatives for Triplet Loss
                unknown_recon = model(batch[1])
                error = tf.reduce_mean(tf.square(batch[1] - unknown_recon), axis=1)
                
                # Triplet Loss Selection
                positive_mask = error < threshold
                negative_mask = error >= threshold
                
                if tf.reduce_sum(tf.cast(positive_mask, tf.float32)) > 0 and tf.reduce_sum(tf.cast(negative_mask, tf.float32)) > 0:
                    positive_samples = tf.boolean_mask(unknown_recon, positive_mask)
                    negative_samples = tf.boolean_mask(unknown_recon, negative_mask)
                    loss_triplet = triplet_loss(reconstructed, positive_samples, negative_samples)
                else:
                    loss_triplet = 0  # Skip triplet loss if no valid samples

                # Total Loss
                loss = recon_loss + 0.5 * loss_triplet  # Adjust triplet loss weight
                total_loss += loss.numpy()
                total_recon_loss += recon_loss.numpy()
                total_triplet_loss += loss_triplet.numpy() if isinstance(loss_triplet, tf.Tensor) else 0

            # Backpropagation
            grads = tape.gradient(loss, model.trainable_variables)
            optimizer.apply_gradients(zip(grads, model.trainable_variables))

        print(f"Epoch [{epoch+1}/{epoch_num}], Total Loss: {total_loss:.4f}, Recon Loss: {total_recon_loss:.4f}, "
              f"Triplet Loss: {total_triplet_loss:.4f}")

    return model

# ==========================
# Main Training Function
# ==========================
def train(known_normal_data, unlabeled_data, x_te, y_te, epoch_num, batch_size, model_file=None, model_type=None, lr_rate=3e-4):
    """
    Main function to train Transformer Autoencoder with Triplet Loss.
    - Uses known normal data and unlabeled data as input.
    - Trains using MSE + Triplet Loss.
    - Updates anomaly threshold dynamically per epoch.
    """

    # Train-Validation Split
    train_x, val_x = known_normal_data[:int(len(known_normal_data) * 0.9)], known_normal_data[int(len(known_normal_data) * 0.9):]
    training_generator = BatchGenerator(train_x, unlabeled_data, batch_size)
    validate_generator = BatchGenerator(val_x, unlabeled_data, batch_size)

    num_train_samples, num_val_samples = len(train_x), len(val_x)

    print(f"Training Samples: {num_train_samples}, Validation Samples: {num_val_samples}")

    # Train Model
    model = train_generator(known_normal_data, unlabeled_data, validate_generator, x_te, y_te, 
                            num_train_samples, num_val_samples, batch_size, epoch_num, model_file, lr_rate)

    return model

# ==========================
# Testing & Evaluation
# ==========================
def test_model(model, x, y, batch_size):
    """
    Evaluates the trained model using:
    - Reconstruction Loss
    - Learned Anomaly Threshold
    - AUC, Precision, Recall, F1-score
    """

    x, y = shuffle(x, y)
    test_loader = BatchGenerator(x, y, batch_size)

    loss_list = []
    for batch_in in test_loader:
        outputs, _ = model.predict(batch_in[0])
        loss = tf.reduce_mean(tf.square(outputs - batch_in[0]), axis=1)
        loss_list.append(loss)

    loss_list = tf.concat(loss_list, axis=0)
    loss = tf.math.reduce_mean(tf.constant(loss_list), axis=1)
    gc.collect()

    # Compute Threshold Using 3-Sigma Rule
    threshold = np.percentile(loss.numpy(), 99.7)
    print(f"Computed Anomaly Threshold: {threshold:.4f}")

    # Anomaly Predictions
    predictions = (loss >= threshold).numpy().astype(int)

    # Compute AUC, Precision, Recall, and F1-score
    FPR_auto, TPR_auto, _ = roc_curve(y, loss)
    auc_auto = auc(FPR_auto, TPR_auto)
    precision, recall, f1_score, _ = precision_recall_fscore_support(y, predictions, average='binary', pos_label=0)

    print(f"AUC - Transformer Autoencoder: {auc_auto:.4f}")
    print(f"Precision: {precision:.4f}, Recall: {recall:.4f}, F1 Score: {f1_score:.4f}")

    return auc_auto

# ==========================
# Main Execution
# ==========================
if __name__ == '__main__':
    tf.debugging.set_log_device_placement(False)

    # Load Data
    x_tr = pickle.load(open(f"../../logs/{dataset}/encoded_data/{name}_x_tr.pkl", "rb"))
    y_tr = pickle.load(open(f"../../logs/{dataset}/encoded_data/{name}_y_tr.pkl", "rb"))
    x_te = pickle.load(open(f"../../logs/{dataset}/encoded_data/{name}_x_te.pkl", "rb"))
    y_te = pickle.load(open(f"../../logs/{dataset}/encoded_data/{name}_y_te.pkl", "rb"))

    print(f"Total Normal Training Samples: {sum(y_tr == 0)}")

    # Split Normal Data into 50% Known Normal + 50% Unlabeled
    normal_samples = [x_tr[i] for i in range(len(y_tr)) if y_tr[i] == 0]
    num_known_normal = len(normal_samples) // 2  # 50% for known normal
    known_normal_data = normal_samples[:num_known_normal]
    unlabeled_data = normal_samples[num_known_normal:] + [x_tr[i] for i in range(len(y_tr)) if y_tr[i] == 1]

    print(f"Known Normal Samples: {len(known_normal_data)}, Unlabeled Samples: {len(unlabeled_data)}")

    # Train Model
    model = train(known_normal_data, unlabeled_data, x_te, y_te, epoch_num=100, batch_size=32, 
                  model_file=f"../saved_model/{dataset}/{exp_no}_{model_name}_{max_len}_{stride}_{dataset}_{name}_transformer.ckpt",
                  lr_rate=0.0003)

    # Test Model
    auc_auto = test_model(model, x_te, y_te, batch_size=1024)