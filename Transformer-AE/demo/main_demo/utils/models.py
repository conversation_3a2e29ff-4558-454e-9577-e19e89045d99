import math
import numpy as np
import torch
import torch.nn as nn

class TripletLoss(nn.Module):
    def __init__(self, margin=1.0):
        super(TripletLoss, self).__init__()
        self.margin = margin

    def forward(self, unknown_normal, unknown_abnormal):
        """
        Calculate triplet loss based on distance to origin in latent space
        - known_normal: latent representation of known normal samples
        - unknown_normal: latent representation of unknown normal samples
        - unknown_abnormal: latent representation of unknown abnormal samples
        """
        # For unknown normal samples (should be close to origin)
        unknown_normal_distance = torch.mean(torch.sum(unknown_normal ** 2, dim=-1))
        
        # For abnormal samples (should be far from origin)
        unknown_abnormal_distance = torch.mean(torch.sum(unknown_abnormal ** 2, dim=-1))
        
        # Combined loss:
        # 1. Minimize distance of known normal to origin
        # 2. Apply triplet constraint between unknown normal and unknown abnormal
        triplet_loss = torch.clamp(unknown_normal_distance - unknown_abnormal_distance + self.margin, min=0.0)
        
        return triplet_loss

class TripletLossLatent(nn.Module):
    def __init__(self, margin=1.0):
        super(TripletLossLatent, self).__init__()
        self.margin = margin

    def forward(self, anchor, positive, negative):
        """
        Calculate triplet loss based on distances in latent space
        - anchor: latent representation of anchor samples
        - positive: latent representation of positive samples
        - negative: latent representation of negative samples
        """
        # Calculate distances
        pos_dist = torch.sum((anchor - positive) ** 2, dim=-1)
        neg_dist = torch.sum((anchor - negative) ** 2, dim=-1)
        
        # Calculate triplet loss
        loss = torch.clamp(pos_dist - neg_dist + self.margin, min=0.0)
        
        return torch.mean(loss)

class TransformerAE(nn.Module):
    def __init__(self, embed_dim=768, num_heads=12, hidden_dims=[256, 128], latent_dim=64, num_layers=1, max_seq_length=512):
        super(TransformerAE, self).__init__()
        self.embed_dim = embed_dim
        self.max_seq_length = max_seq_length
        
        # Set seed for reproducibility
        torch.manual_seed(42)

        # Input layer (in PyTorch, this is handled implicitly)
        
        # Positional encoding
        self.positional_encoding = self.create_positional_encoding(max_seq_length, embed_dim)

        # Transformer Encoder layers
        self.encoder_layers = nn.ModuleList([
            nn.ModuleList([
                nn.MultiheadAttention(embed_dim=embed_dim, num_heads=num_heads, batch_first=True),
                nn.LayerNorm(embed_dim, eps=1e-6),
                nn.Linear(embed_dim, embed_dim),
                nn.ReLU(),
                nn.LayerNorm(embed_dim, eps=1e-6)
            ]) for _ in range(num_layers)
        ])
        
        # Latent space layers
        self.fc_encoder_hidden_layers = nn.ModuleList([])
        current_dim = self.embed_dim
        for hidden_dim in hidden_dims:
            self.fc_encoder_hidden_layers.append(nn.Linear(current_dim, hidden_dim))
            self.fc_encoder_hidden_layers.append(nn.ReLU())
            current_dim = hidden_dim

        # Final latent layer
        self.fc_latent = nn.Linear(hidden_dims[-1], latent_dim)

        # Decoder layers
        self.fc_decoder_hidden_layers = nn.ModuleList([])
        current_dim = latent_dim
        for hidden_dim in reversed(hidden_dims):
            self.fc_decoder_hidden_layers.append(nn.Linear(current_dim, hidden_dim))
            self.fc_decoder_hidden_layers.append(nn.ReLU())
            current_dim = hidden_dim

        # Output layer
        self.fc_out = nn.Linear(hidden_dims[0], embed_dim)
        
        # Initialize all weights using Xavier uniform (equivalent to GlorotUniform in TF)
        self._init_weights()

    def _init_weights(self):
        """Initialize all layers using Xavier uniform initializer (equivalent to GlorotUniform)"""
        for name, module in self.named_modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.MultiheadAttention):
                # Initialize the projection weights in MultiheadAttention
                nn.init.xavier_uniform_(module.in_proj_weight)
                nn.init.xavier_uniform_(module.out_proj.weight)
                if module.in_proj_bias is not None:
                    nn.init.zeros_(module.in_proj_bias)
                if module.out_proj.bias is not None:
                    nn.init.zeros_(module.out_proj.bias)

    def create_positional_encoding(self, max_len, d_model):
        """Create positional encoding similar to the TF implementation"""
        position = np.arange(max_len)[:, np.newaxis]
        div_term = np.exp(np.arange(0, d_model, 2) * -(np.log(10000.0) / d_model))
        pos_encoding = np.zeros((max_len, d_model))
        pos_encoding[:, 0::2] = np.sin(position * div_term)
        pos_encoding[:, 1::2] = np.cos(position * div_term)
        pos_encoding = pos_encoding[np.newaxis, ...]
        return torch.FloatTensor(pos_encoding).squeeze(0)

    def forward(self, x, return_latent=False):
        # Add positional encoding
        x = x + self.positional_encoding[:x.size(1), :].to(x.device)
        
        # Encoder part
        for attention_layer, norm1, dense_ffn, relu, norm2 in self.encoder_layers:
            # Self-attention
            attn_output, _ = attention_layer(x, x, x)
            x = norm1(x + attn_output)  # Add & Norm
            
            # Feed-forward network
            ffn_output = dense_ffn(x)
            ffn_output = relu(ffn_output)
            x = norm2(x + ffn_output)  # Add & Norm
        
        # Store encoder output for potential return
        encoder_output = x.clone()
        
        # Latent space
        for layer in self.fc_encoder_hidden_layers:
            x = layer(x)
        
        # Get latent representation
        latent = self.fc_latent(x)
        
        # Start decoder path
        x = latent
        
        # Decoder part
        for layer in self.fc_decoder_hidden_layers:
            x = layer(x)
            
        # Output layer
        output = self.fc_out(x)
        
        if return_latent:
            return output, latent
        return output

class TransformerWithClassifierHead(nn.Module):
    """
    Extended TransformerAE model with an additional classification head
    attached to the latent representation layer.
    
    This model has two missions:
    1. Reconstruct the input data (especially normal data)
    2. Classify between normal and abnormal data
    """
    def __init__(self, embed_dim=768, num_heads=12, hidden_dims=[512, 128], 
                 latent_dim=64, num_layers=2, max_seq_length=20, classifier_dims=[64, 32, 16, 8]):
        super().__init__()
        self.autoencoder = TransformerAE(
            embed_dim=embed_dim,
            num_heads=num_heads,
            hidden_dims=hidden_dims,
            latent_dim=latent_dim,
            num_layers=num_layers,
            max_seq_length=max_seq_length
        )
        
        # Simple classifier - single linear layer with sigmoid activation
        final_layer = nn.Linear(latent_dim, 1)
        nn.init.xavier_uniform_(final_layer.weight)
        nn.init.zeros_(final_layer.bias)
        
        self.classifier = nn.Sequential(
            final_layer,
            nn.Sigmoid()
        )
    
    def forward(self, x, return_latent=False, return_classifier=False):
        """
        Forward pass through the model.
        
        Args:
            x: Input tensor of shape [batch_size, seq_length, embed_dim]
            return_latent: Whether to return the latent representation
            return_classifier: Whether to return the classifier output
            
        Returns:
            Tuple of (reconstruction, latent, classification) where:
            - reconstruction: Reconstructed input [batch_size, seq_length, embed_dim]
            - latent: Latent representation [batch_size, latent_dim] (if return_latent=True)
            - classification: Binary classification [batch_size, 1] (if return_classifier=True)
        """
        # Get reconstruction and latent space from autoencoder
        reconstruction, latent = self.autoencoder(x, return_latent=True)
        
        # Initialize return values
        latent_out = None
        classification = None
        
        if return_latent or return_classifier:
            # Reshape latent to [batch_size, latent_dim]
            latent_pooled = torch.mean(latent, dim=1)  # Mean pooling over sequence length
            latent_out = latent_pooled
            
            if return_classifier:
                # Pass through classifier
                classification = self.classifier(latent_pooled)
        
        # Return appropriate values based on flags
        if return_latent and return_classifier:
            return reconstruction, latent, classification
        elif return_latent:
            return reconstruction, latent
        elif return_classifier:
            return reconstruction, classification
        else:
            return reconstruction
    
    def get_autoencoder(self):
        """
        Return only the autoencoder part for anomaly detection
        
        Returns:
            The autoencoder component of this model (the parent TransformerAE)
        """
        return self.autoencoder
