import numpy as np
import torch
from torch.utils.data import Dataset, Sampler
from sklearn.utils import shuffle
import random

class CustomDataset(Dataset):
    def __init__(self, data, labels, true_labels):
        self.data = np.array(data, dtype=np.float32)
        self.labels = np.array(labels, dtype=np.int64)
        self.true_labels = np.array(true_labels, dtype=np.int64)
        self.data, self.labels, self.true_labels = shuffle(self.data, self.labels, self.true_labels)

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        sample = torch.tensor(self.data[idx])
        label = torch.tensor(self.labels[idx])
        true_label = torch.tensor(self.true_labels[idx])
        return sample, label, true_label

    def get_balanced_batch(self, batch_size):
        normal_indices = np.where(self.labels == 0)[0]
        unknown_indices = np.where(self.labels == 1)[0]
        normal_samples = np.random.choice(normal_indices, batch_size // 2, replace=False)
        unknown_samples = np.random.choice(unknown_indices, batch_size // 2, replace=False)
        batch_indices = np.concatenate((normal_samples, unknown_samples))
        np.random.shuffle(batch_indices)
        return [self[i] for i in batch_indices]

class BalancedBatchSampler(Sampler):
    """
    Ensures every batch has a balanced mixture of known normal and unknown data
    """
    def __init__(self, dataset, batch_size, normal_ratio=0.5):
        self.dataset = dataset
        self.batch_size = batch_size
        self.normal_ratio = normal_ratio
        
        # Get indices of normal and unknown samples
        self.normal_indices = np.where(self.dataset.labels == 0)[0]
        self.unknown_indices = np.where(self.dataset.labels == 1)[0]
        
        # Calculate number of samples per batch
        self.normal_per_batch = int(self.batch_size * self.normal_ratio)
        self.unknown_per_batch = self.batch_size - self.normal_per_batch
        
        # Calculate number of complete batches
        self.num_batches = min(
            len(self.normal_indices) // self.normal_per_batch,
            len(self.unknown_indices) // self.unknown_per_batch
        )
        
    def __iter__(self):
        # Shuffle indices for each epoch
        np.random.shuffle(self.normal_indices)
        np.random.shuffle(self.unknown_indices)
        
        for batch_idx in range(self.num_batches):
            batch = []
            
            # Get normal samples for this batch
            normal_idx_start = batch_idx * self.normal_per_batch
            normal_idx_end = normal_idx_start + self.normal_per_batch
            batch.extend(self.normal_indices[normal_idx_start:normal_idx_end])
            
            # Get unknown samples for this batch
            unknown_idx_start = batch_idx * self.unknown_per_batch
            unknown_idx_end = unknown_idx_start + self.unknown_per_batch
            batch.extend(self.unknown_indices[unknown_idx_start:unknown_idx_end])
            
            # Shuffle the batch
            random.shuffle(batch)
            
            # Yield the entire batch of indices at once
            yield batch
    
    def __len__(self):
        return self.num_batches
