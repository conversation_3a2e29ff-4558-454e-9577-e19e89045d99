import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from tqdm import tqdm
import logging
import copy
import pickle
from sklearn.utils import shuffle

from .models import TransformerAE
from .data import CustomDataset
from .evaluation import compute_threshold, compute_threshold_v2

logger = logging.getLogger(__name__)

def train_generator(train_loader, validate_loader, batch_size, epoch_num, model_file=None, lr_rate=3e-4, device='cuda'):

    # Create model
    model = TransformerAE(embed_dim=768, num_heads=12, hidden_dims=[512, 128], \
                          num_layers=2, max_seq_length=20, latent_dim=64).to(device)
    
    # Setup optimizer with learning rate scheduling
    # Similar to the TF optimization with warmup
    num_train_steps = len(train_loader) * epoch_num
    num_warmup_steps = int(0.0001 * num_train_steps)
    
    # Create optimizer
    optimizer = optim.AdamW(model.parameters(), lr=lr_rate)
    
    # Create scheduler similar to TF warmup with polynomial warmup
    def lr_lambda(current_step):
        # Implements polynomial warmup similar to TensorFlow WarmUp scheduler
        if current_step < num_warmup_steps:
            # Using power=1.0 for linear warmup by default
            warmup_percent_done = float(current_step) / float(max(1, num_warmup_steps))
            power = 1.0
            return warmup_percent_done ** power
        
        # Linear decay after warmup period
        return max(0.0, float(num_train_steps - current_step) / float(max(1, num_train_steps - num_warmup_steps)))
    
    scheduler = optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)

    # Loss function
    criterion = nn.MSELoss()

    best_val_loss = float('inf')
    best_model = None
    patience = 5
    patience_counter = 0
    
    # Parameters for dynamic categorization
    categorization_patience = patience  # Use same value as early stopping patience
    gaining_patience = 3  # How many consecutive categorizations without new abnormal samples to stop
    gaining_patience_counter = 0
    categorize_flag = False  # Flag to trigger categorization
    
    # Loss weights
    w_known = 1.0       # Weight for known normal reconstruction
    w_known_abnormal = 0.0  # Weight for known abnormal reconstruction (maximize)
    w_unknown_normal = 0.0  # Weight for unknown normal reconstruction (minimize)
    w_unknown_abnormal = 100.0  # Weight for unknown abnormal reconstruction (maximize)
    margin = 3.0        # Margin for unknown abnormal reconstruction loss
    
    # Extract all data from Train DataLoader for dynamic sample categorization
    all_train_data = []
    all_train_labels = []
    all_train_true_labels = []
    
    for batch in train_loader:
        batch_data, batch_labels, batch_true_labels = batch
        all_train_data.append(batch_data.numpy())
        all_train_labels.append(batch_labels.numpy())
        all_train_true_labels.append(batch_true_labels.numpy())
    
    all_train_data = np.concatenate(all_train_data, axis=0)
    all_train_labels = np.concatenate(all_train_labels, axis=0)
    all_train_true_labels = np.concatenate(all_train_true_labels, axis=0)
    
    # Fixed categorization percentage (0.5%)
    normal_categorization_percent = 0.005  # Fixed at 0.5%
    abnormal_categorization_percent = 0.005  # Fixed at 0.5%
    
    # Create separate sets for each category in training
    known_normal_indices = np.where(all_train_labels == 0)[0]
    unknown_indices = np.where(all_train_labels == 1)[0].tolist()  # Start with all unknown samples
    unknown_normal_indices = []  # Will accumulate categorized normal samples
    unknown_abnormal_indices = []  # Will accumulate categorized abnormal samples
    
    # Extract all data from Validation DataLoader for dynamic sample categorization
    all_val_data = []
    all_val_labels = []
    all_val_true_labels = []
    
    for batch in validate_loader:
        batch_data, batch_labels, batch_true_labels = batch
        all_val_data.append(batch_data.numpy())
        all_val_labels.append(batch_labels.numpy())
        all_val_true_labels.append(batch_true_labels.numpy())
    
    all_val_data = np.concatenate(all_val_data, axis=0)
    all_val_labels = np.concatenate(all_val_labels, axis=0)
    all_val_true_labels = np.concatenate(all_val_true_labels, axis=0)
    
    # Create separate sets for each category in validation
    val_known_normal_indices = np.where(all_val_labels == 0)[0]
    val_unknown_indices = np.where(all_val_labels == 1)[0].tolist()  # Start with all unknown samples
    val_unknown_normal_indices = []  # Will accumulate categorized normal samples
    val_unknown_abnormal_indices = []  # Will accumulate categorized abnormal samples
    
    logger.info(f"Initial split - Training: Known normal: {len(known_normal_indices)}, Unknown: {len(unknown_indices)}")
    logger.info(f"Initial split - Validation: Known normal: {len(val_known_normal_indices)}, Unknown: {len(val_unknown_indices)}")

    # Training Loop 
    for epoch in range(epoch_num):
        model.train()
        
        # Dynamic sample categorization when validation loss doesn't improve for 'patience' epochs
        if patience_counter >= categorization_patience:
            categorize_flag = True
            
        if categorize_flag and len(unknown_indices) > 0:
            logger.info(f"Epoch {epoch+1}: Validation hasn't improved for {patience_counter} epochs. Categorizing unknown samples...")
            logger.info(f"Current categorization rate: {normal_categorization_percent*100:.1f}% (fixed)")
            
            # Reset categorization flag
            categorize_flag = False
            
            # Calculate threshold based on current model state
            logger.info(f"Epoch {epoch+1}: Computing anomaly threshold for dynamic categorization...")
            # current_threshold = compute_threshold_v2(validate_loader, best_model, device)
            current_threshold = compute_threshold(train_loader, best_model, 99.7, device)

            logger.info(f"Computed optimal threshold using v2 method: {current_threshold:.6f}")

            # Create a temporary dataset and loader for remaining unknown samples
            unknown_data_subset = all_train_data[unknown_indices]
            unknown_labels_subset = all_train_labels[unknown_indices]  # All 1's
            unknown_true_labels_subset = all_train_true_labels[unknown_indices]  # Actual labels
            
            temp_dataset = CustomDataset(unknown_data_subset, unknown_labels_subset, unknown_true_labels_subset)
            temp_loader = DataLoader(temp_dataset, batch_size=batch_size, shuffle=True)
            
            # Compute reconstruction errors for all remaining unknown samples
            model.eval()
            reconstruction_errors = []
            with torch.no_grad():
                for batch in temp_loader:
                    data, _, _ = batch
                    data = data.to(device)
                    reconstructed = best_model(data)
                    error = ((data - reconstructed) ** 2).mean(dim=(1, 2)).cpu().detach().numpy()
                    reconstruction_errors.append(error)
            
            # Create a mapping of unknown indices to their reconstruction errors
            reconstruction_errors = np.concatenate(reconstruction_errors)
            index_to_error = {idx: error for idx, error in zip(unknown_indices, reconstruction_errors)}

            # Separate samples based on threshold into normal and abnormal partitions
            normal_partition = [idx for idx in unknown_indices if index_to_error[idx] < current_threshold]
            abnormal_partition = [idx for idx in unknown_indices if index_to_error[idx] >= current_threshold]
            
            # Verify partitioning against ground truth labels
            if normal_partition:
                true_normal_count = sum(all_train_true_labels[idx] == 0 for idx in normal_partition)
                normal_accuracy = (true_normal_count / len(normal_partition)) * 100
                logger.info(f"Normal partition accuracy: {normal_accuracy:.2f}% ({true_normal_count}/{len(normal_partition)} are true normal)")

            if abnormal_partition:
                true_abnormal_count = sum(all_train_true_labels[idx] == 1 for idx in abnormal_partition)
                abnormal_accuracy = (true_abnormal_count / len(abnormal_partition)) * 100
                logger.info(f"Abnormal partition accuracy: {abnormal_accuracy:.2f}% ({true_abnormal_count}/{len(abnormal_partition)} are true abnormal)")
            
            logger.info(f"Threshold-based partitioning - Normal: {len(normal_partition)}, Abnormal: {len(abnormal_partition)}")
            
            # Process normal partition if not empty
            new_normal_indices = []
            if normal_partition:
                # Sort normal partition by reconstruction error
                normal_partition.sort(key=lambda idx: index_to_error[idx])
                
                # Calculate percentage for selection
                normal_select_count = max(int(normal_categorization_percent * len(normal_partition)), 1)
                
                # Select samples with lowest errors as new normal samples
                new_normal_indices = normal_partition[:normal_select_count]
                unknown_normal_indices.extend(new_normal_indices)
                
                # Check accuracy of normal categorization
                true_normal_count = sum(all_train_true_labels[idx] == 0 for idx in new_normal_indices)
                normal_accuracy = (true_normal_count / len(new_normal_indices)) * 100 if new_normal_indices else 0
                logger.info(f"Normal categorization accuracy: {normal_accuracy:.2f}% "
                    f"({true_normal_count}/{len(new_normal_indices)})")
            
            # Process abnormal partition if not empty
            new_abnormal_indices = []
            if abnormal_partition:
                # Sort abnormal partition by reconstruction error
                abnormal_partition.sort(key=lambda idx: index_to_error[idx])
                
                # Calculate percentage for selection
                abnormal_select_count = max(int(abnormal_categorization_percent * len(abnormal_partition)), 1)
                
                # Select samples with highest errors as new abnormal samples
                new_abnormal_indices = abnormal_partition[-abnormal_select_count:]
                unknown_abnormal_indices.extend(new_abnormal_indices)
                
                # Check accuracy of abnormal categorization
                true_abnormal_count = sum(all_train_true_labels[idx] == 1 for idx in new_abnormal_indices)
                abnormal_accuracy = (true_abnormal_count / len(new_abnormal_indices)) * 100 if new_abnormal_indices else 0
                logger.info(f"Abnormal categorization accuracy: {abnormal_accuracy:.2f}% "
                    f"({true_abnormal_count}/{len(new_abnormal_indices)})")
            
            # Check if we've gained new abnormal samples
            if len(new_abnormal_indices) == 0:
                gaining_patience_counter += 1
                logger.info(f"No new abnormal samples gained. Gaining patience: {gaining_patience_counter}/{gaining_patience}")
            else:
                gaining_patience_counter = 0  # Reset the counter if we found new samples
            
            # Remove selected samples from unknown_indices
            selected_indices = set(new_normal_indices + new_abnormal_indices)
            unknown_indices = [idx for idx in unknown_indices if idx not in selected_indices]
            
            logger.info(f"Training categorized - New normal: {len(new_normal_indices)}, "
                f"New abnormal: {len(new_abnormal_indices)}, "
                f"Remaining unknown: {len(unknown_indices)}")
            logger.info(f"Training total categorized - Normal: {len(unknown_normal_indices)}, "
                f"Abnormal: {len(unknown_abnormal_indices)}, "
                f"Remaining unknown: {len(unknown_indices)}")
            
            # Process validation samples with the same approach
            if len(val_unknown_indices) > 0:
                # Create a temporary dataset and loader for remaining unknown validation samples
                val_unknown_data_subset = all_val_data[val_unknown_indices]
                val_unknown_labels_subset = all_val_labels[val_unknown_indices]  # All 1's
                val_unknown_true_labels_subset = all_val_true_labels[val_unknown_indices]  # Actual labels
                
                val_temp_dataset = CustomDataset(val_unknown_data_subset, val_unknown_labels_subset, val_unknown_true_labels_subset)
                val_temp_loader = DataLoader(val_temp_dataset, batch_size=batch_size, shuffle=False)
                
                # Compute reconstruction errors for all remaining unknown validation samples
                model.eval()
                val_reconstruction_errors = []
                with torch.no_grad():
                    for batch in val_temp_loader:
                        data, _, _ = batch
                        data = data.to(device)
                        reconstructed = best_model(data)
                        error = ((data - reconstructed) ** 2).mean(dim=(1, 2)).cpu().detach().numpy()
                        val_reconstruction_errors.append(error)
                
                # Create a mapping of unknown validation indices to their reconstruction errors
                val_reconstruction_errors = np.concatenate(val_reconstruction_errors)
                val_index_to_error = {idx: error for idx, error in zip(val_unknown_indices, val_reconstruction_errors)}

                # Separate samples based on threshold into normal and abnormal partitions
                val_normal_partition = [idx for idx in val_unknown_indices if val_index_to_error[idx] < current_threshold]
                val_abnormal_partition = [idx for idx in val_unknown_indices if val_index_to_error[idx] >= current_threshold]

                logger.info(f"Validation threshold-based partitioning - Normal: {len(val_normal_partition)}, Abnormal: {len(val_abnormal_partition)}")

                # Process normal partition if not empty
                val_new_normal_indices = []
                if val_normal_partition:
                    # Sort normal partition by reconstruction error
                    val_normal_partition.sort(key=lambda idx: val_index_to_error[idx])
                    
                    # Calculate percentage for selection
                    val_normal_select_count = max(int(normal_categorization_percent * len(val_normal_partition)), 1)
                    
                    # Select samples with lowest errors as new normal samples
                    val_new_normal_indices = val_normal_partition[:val_normal_select_count]
                    val_unknown_normal_indices.extend(val_new_normal_indices)
                    
                    # Check accuracy of validation normal categorization
                    val_true_normal_count = sum(all_val_true_labels[idx] == 0 for idx in val_new_normal_indices)
                    val_normal_accuracy = (val_true_normal_count / len(val_new_normal_indices)) * 100 if val_new_normal_indices else 0
                    logger.info(f"Validation normal categorization accuracy: {val_normal_accuracy:.2f}% "
                        f"({val_true_normal_count}/{len(val_new_normal_indices)})")

                # Process abnormal partition if not empty
                val_new_abnormal_indices = []
                if val_abnormal_partition:
                    # Sort abnormal partition by reconstruction error
                    val_abnormal_partition.sort(key=lambda idx: val_index_to_error[idx])
                    
                    # Calculate percentage for selection and noise removal
                    noise_removal = max(int(0.01 * len(val_abnormal_partition)), 1)
                    val_abnormal_select_count = max(int(abnormal_categorization_percent * len(val_abnormal_partition)), 1)
                    
                    # Remove 1% lowest errors as noise
                    # val_abnormal_partition = val_abnormal_partition[noise_removal:]
                    
                    # Select samples with highest errors as new abnormal samples
                    val_new_abnormal_indices = val_abnormal_partition[-val_abnormal_select_count:]
                    val_unknown_abnormal_indices.extend(val_new_abnormal_indices)
                    
                    # Check accuracy of validation abnormal categorization
                    val_true_abnormal_count = sum(all_val_true_labels[idx] == 1 for idx in val_new_abnormal_indices)
                    val_abnormal_accuracy = (val_true_abnormal_count / len(val_new_abnormal_indices)) * 100 if val_new_abnormal_indices else 0
                    logger.info(f"Validation abnormal categorization accuracy: {val_abnormal_accuracy:.2f}% "
                        f"({val_true_abnormal_count}/{len(val_new_abnormal_indices)})")

                # Remove selected samples from unknown validation indices
                val_selected_indices = set(val_new_normal_indices + val_new_abnormal_indices)
                val_unknown_indices = [idx for idx in val_unknown_indices if idx not in val_selected_indices]

                logger.info(f"Validation categorized - New normal: {len(val_new_normal_indices)}, "
                    f"New abnormal: {len(val_new_abnormal_indices)}, "
                    f"Remaining unknown: {len(val_unknown_indices)}")
                logger.info(f"Validation total categorized - Normal: {len(val_unknown_normal_indices)}, "
                    f"Abnormal: {len(val_unknown_abnormal_indices)}, "
                    f"Remaining unknown: {len(val_unknown_indices)}")
            
            # Create new training dataset with categorized samples
            new_train_labels = all_train_labels.copy()
            
            # Mark categorized samples in the labels array (2=unknown normal, 3=unknown abnormal)
            for idx in unknown_normal_indices:
                new_train_labels[idx] = 2  # Custom label for unknown normal
            for idx in unknown_abnormal_indices:
                new_train_labels[idx] = 3  # Custom label for unknown abnormal
            
            # Create new training dataset and loader with updated labels
            new_train_dataset = CustomDataset(all_train_data, new_train_labels, all_train_true_labels)
            train_loader = DataLoader(new_train_dataset, batch_size=batch_size, shuffle=True)
            
            # Create new validation dataset with categorized samples
            new_val_labels = all_val_labels.copy()
            
            # Mark categorized samples in the validation labels array
            for idx in val_unknown_normal_indices:
                new_val_labels[idx] = 2  # Custom label for unknown normal
            for idx in val_unknown_abnormal_indices:
                new_val_labels[idx] = 3  # Custom label for unknown abnormal
            
            # Create new validation dataset and loader with updated labels
            new_val_dataset = CustomDataset(all_val_data, new_val_labels, all_val_true_labels)
            validate_loader = DataLoader(new_val_dataset, batch_size=batch_size, shuffle=True)
            
            # Reset validation loss improvement tracking
            best_val_loss = float("inf")
            patience_counter = 0

            # Check if we should stop categorizing based on gaining_patience
            if gaining_patience_counter >= gaining_patience:
                logger.info(f"No new abnormal samples gained for {gaining_patience} consecutive attempts. Stopping categorization.")
                # We will stop categorizing but continue training
            
            # Switch back to training mode
            model.train()

        # Log current learning rate
        current_lr = optimizer.param_groups[0]['lr']
        logger.info(f"Epoch [{epoch+1}/{epoch_num}] - Current learning rate: {current_lr:.6f}")

        total_loss = 0
        total_known_loss = 0
        total_known_abnormal_loss = 0  # New counter for known abnormal loss
        total_unknown_normal_loss = 0
        total_unknown_abnormal_loss = 0
        batch_count = 0
        
        # Counters for samples in each category
        total_known_samples = 0
        total_known_abnormal_samples = 0  # New counter for known abnormal samples
        total_unknown_normal_samples = 0
        total_unknown_abnormal_samples = 0
        total_uncategorized_samples = 0

        for batch in tqdm(train_loader, desc=f"Training Epoch {epoch+1}/{epoch_num}"):
            data, labels, _ = batch
            data = data.to(device)
            labels = labels.to(device)

            # Process different categories based on our custom labels
            known_normal_mask = labels == 0
            known_abnormal_mask = labels == 4  # Known abnormal samples
            unknown_mask = labels == 1  # Uncategorized unknown samples
            unknown_normal_mask = labels == 2  # Pre-categorized unknown normal
            unknown_abnormal_mask = labels == 3  # Pre-categorized unknown abnormal
            
            known_normal_batch = data[known_normal_mask]
            known_abnormal_batch = data[known_abnormal_mask]  # New: Known abnormal samples
            unknown_batch = data[unknown_mask]
            unknown_normal_batch = data[unknown_normal_mask]
            unknown_abnormal_batch = data[unknown_abnormal_mask]

            # Count samples in each category
            total_known_samples += known_normal_mask.sum().item()
            total_known_abnormal_samples += known_abnormal_mask.sum().item()  # New counter
            total_unknown_normal_samples += unknown_normal_mask.sum().item()
            total_unknown_abnormal_samples += unknown_abnormal_mask.sum().item()
            total_uncategorized_samples += unknown_mask.sum().item()

            optimizer.zero_grad()
            
            # Initialize losses
            known_normal_loss = torch.tensor(0.0).to(device)
            known_abnormal_loss = torch.tensor(0.0).to(device)  # New: Loss for known abnormal samples
            unknown_normal_loss = torch.tensor(0.0).to(device)
            unknown_abnormal_loss = torch.tensor(0.0).to(device)
            
            # Process known normal data
            if len(known_normal_batch) > 0:
                known_normal_output = model(known_normal_batch)
                known_normal_loss = criterion(known_normal_batch, known_normal_output)
            
            # Process known abnormal data - NEW CODE
            if len(known_abnormal_batch) > 0:
                known_abnormal_output = model(known_abnormal_batch)
                abnormal_recon_error = criterion(known_abnormal_batch, known_abnormal_output)
                epsilon = 1e-8
                known_abnormal_loss = 1.0 / (abnormal_recon_error + epsilon)  # Inverse to maximize error

            # Process pre-categorized unknown normal samples
            if len(unknown_normal_batch) > 0:
                unknown_normal_output = model(unknown_normal_batch)
                unknown_normal_loss = criterion(unknown_normal_batch, unknown_normal_output)
            
            # Process pre-categorized unknown abnormal samples
            if len(unknown_abnormal_batch) > 0:
                unknown_abnormal_output = model(unknown_abnormal_batch)
                abnormal_recon_error = criterion(unknown_abnormal_batch, unknown_abnormal_output)
                epsilon = 1e-8
                unknown_abnormal_loss = 1.0 / (abnormal_recon_error + epsilon)

            # Combined loss with new term for known abnormal samples
            loss = (w_known * known_normal_loss + 
                   w_known_abnormal * known_abnormal_loss +
                   w_unknown_normal * unknown_normal_loss + 
                   w_unknown_abnormal * unknown_abnormal_loss)
            
            loss.backward()
            optimizer.step()
            scheduler.step()

            total_loss += loss.item()
            total_known_loss += known_normal_loss.item()
            total_known_abnormal_loss += known_abnormal_loss.item()  # Track new loss
            total_unknown_normal_loss += unknown_normal_loss.item()
            total_unknown_abnormal_loss += unknown_abnormal_loss.item()
            batch_count += 1

        # Calculate average losses
        avg_loss = total_loss / batch_count if batch_count > 0 else 0.0
        avg_known_loss = total_known_loss / batch_count if batch_count > 0 else 0.0
        avg_known_abnormal_loss = total_known_abnormal_loss / batch_count if batch_count > 0 else 0.0  # New average loss
        avg_unknown_normal_loss = total_unknown_normal_loss / batch_count if batch_count > 0 else 0.0
        avg_unknown_abnormal_loss = total_unknown_abnormal_loss / batch_count if batch_count > 0 else 0.0
        
        # Log total counts of samples in each category for this epoch
        logger.info(f"Epoch [{epoch+1}/{epoch_num}] Summary - "
                   f"Known normal samples: {total_known_samples}, "
                   f"Known abnormal samples: {total_known_abnormal_samples}, "  # Add to log
                   f"Unknown normal samples: {total_unknown_normal_samples}, "
                   f"Unknown abnormal samples: {total_unknown_abnormal_samples}, "
                   f"Uncategorized samples: {total_uncategorized_samples}")

        # Validation
        model.eval()
        val_loss = 0
        val_known_loss = 0
        val_known_abnormal_loss = 0  # New counter for validation known abnormal loss
        val_unknown_normal_loss = 0
        val_unknown_abnormal_loss = 0
        val_batch_count = 0
        
        # Counters for validation samples in each category
        val_total_known_samples = 0
        val_total_known_abnormal_samples = 0  # New counter for validation known abnormal samples
        val_total_unknown_normal_samples = 0
        val_total_unknown_abnormal_samples = 0
        val_total_uncategorized_samples = 0
        
        with torch.no_grad():
            for batch in tqdm(validate_loader, desc="Validating"):
                data, labels, _ = batch
                data = data.to(device)
                labels = labels.to(device)

                # Process validation data using same categorization as training
                known_normal_mask = labels == 0
                known_abnormal_mask = labels == 4  # Known abnormal samples
                unknown_mask = labels == 1  # Uncategorized unknown samples
                unknown_normal_mask = labels == 2  # Pre-categorized unknown normal
                unknown_abnormal_mask = labels == 3  # Pre-categorized unknown abnormal
                
                known_normal_batch = data[known_normal_mask]
                known_abnormal_batch = data[known_abnormal_mask]  # New: Known abnormal samples
                unknown_batch = data[unknown_mask]
                unknown_normal_batch = data[unknown_normal_mask]
                unknown_abnormal_batch = data[unknown_abnormal_mask]
                
                # Count validation samples in each category
                val_total_known_samples += known_normal_mask.sum().item()
                val_total_known_abnormal_samples += known_abnormal_mask.sum().item()  # New counter
                val_total_unknown_normal_samples += unknown_normal_mask.sum().item()
                val_total_unknown_abnormal_samples += unknown_abnormal_mask.sum().item()
                val_total_uncategorized_samples += unknown_mask.sum().item()

                # Initialize losses
                known_normal_loss = torch.tensor(0.0).to(device)
                known_abnormal_loss = torch.tensor(0.0).to(device)  # New: Loss for validation known abnormal samples
                unknown_normal_loss = torch.tensor(0.0).to(device)
                unknown_abnormal_loss = torch.tensor(0.0).to(device)
                
                # Process known normal data
                if len(known_normal_batch) > 0:
                    known_normal_output = model(known_normal_batch)
                    known_normal_loss = criterion(known_normal_batch, known_normal_output)
                
                # Process known abnormal data - NEW CODE
                if len(known_abnormal_batch) > 0:
                    known_abnormal_output = model(known_abnormal_batch)
                    abnormal_recon_error = criterion(known_abnormal_batch, known_abnormal_output)
                    epsilon = 1e-8
                    known_abnormal_loss = 1.0 / (abnormal_recon_error + epsilon)  # Inverse to maximize error
                
                # Process unknown normal data
                if len(unknown_normal_batch) > 0:
                    unknown_normal_output = model(unknown_normal_batch)
                    unknown_normal_loss = criterion(unknown_normal_batch, unknown_normal_output)
                
                # Process unknown abnormal data
                if len(unknown_abnormal_batch) > 0:
                    unknown_abnormal_output = model(unknown_abnormal_batch)
                    abnormal_recon_error = criterion(unknown_abnormal_batch, unknown_abnormal_output)
                    epsilon = 1e-8
                    unknown_abnormal_loss = 1.0 / (abnormal_recon_error+epsilon)
                
                # Combined loss for validation - same approach as training
                val_batch_loss = (w_known * known_normal_loss + 
                                 w_known_abnormal * known_abnormal_loss +  # Include new term
                                 w_unknown_normal * unknown_normal_loss + 
                                 w_unknown_abnormal * unknown_abnormal_loss)
                
                val_loss += val_batch_loss.item()
                val_known_loss += known_normal_loss.item()
                val_known_abnormal_loss += known_abnormal_loss.item()  # Track new loss
                val_unknown_normal_loss += unknown_normal_loss.item()
                val_unknown_abnormal_loss += unknown_abnormal_loss.item()
                val_batch_count += 1

        val_loss = val_loss / val_batch_count if val_batch_count > 0 else float('inf')
        val_known_loss = val_known_loss / val_batch_count if val_batch_count > 0 else 0.0
        val_known_abnormal_loss = val_known_abnormal_loss / val_batch_count if val_batch_count > 0 else 0.0  # New average loss
        val_unknown_normal_loss = val_unknown_normal_loss / val_batch_count if val_batch_count > 0 else 0.0
        val_unknown_abnormal_loss = val_unknown_abnormal_loss / val_batch_count if val_batch_count > 0 else 0.0
        
        logger.info(f"Validation Summary - "
                   f"Known normal samples: {val_total_known_samples}, "
                   f"Known abnormal samples: {val_total_known_abnormal_samples}, "  # Add to log
                   f"Unknown normal samples: {val_total_unknown_normal_samples}, "
                   f"Unknown abnormal samples: {val_total_unknown_abnormal_samples}, "
                   f"Uncategorized samples: {val_total_uncategorized_samples}")

        logger.info(f"Epoch [{epoch+1}/{epoch_num}] - "
            f"Train Loss: {avg_loss:.4f} (Known: {avg_known_loss:.4f}, Known Abnormal: {avg_known_abnormal_loss:.4f}, "
            f"Unknown Normal: {avg_unknown_normal_loss:.4f}, Unknown Abnormal: {avg_unknown_abnormal_loss:.4f}), "
            f"Val Loss: {val_loss:.4f} (Known: {val_known_loss:.4f}, Known Abnormal: {val_known_abnormal_loss:.4f}, "
            f"Unknown Normal: {val_unknown_normal_loss:.4f}, Unknown Abnormal: {val_unknown_abnormal_loss:.4f})")

        # Step the learning rate scheduler based on validation loss
        # scheduler.step(val_loss)

        # Early Stopping and validation improvement tracking
        if val_loss < best_val_loss:
            logger.info(f"Early Stopping: Validation loss improved from {best_val_loss:.4f} to {val_loss:.4f}")
            best_val_loss = val_loss
            best_model = copy.deepcopy(model)
            patience_counter = 0  # Reset patience counter when validation improves
            torch.save(model.state_dict(), model_file)
            logger.info(f"Model saved to {model_file}")
        else:
            patience_counter += 1
            logger.info(f"Early Stopping: No improvement. Patience {patience_counter}/{patience}, "
              f"Best val_loss: {best_val_loss:.4f}, Current: {val_loss:.4f}")

        # Check for early stopping condition
        if patience_counter >= patience:
            # Instead of stopping immediately, check if we should try categorization first
            if len(unknown_indices) > 0 and gaining_patience_counter < gaining_patience:
                logger.info(f"No validation improvement for {patience_counter} epochs. Trying categorization before stopping.")
                categorize_flag = True
            else:
                logger.info("Early stopping triggered - no more unknown data or no benefit from categorization")
                break

    # Calculate threshold only at the end of training for testing purposes
    best_threshold = compute_threshold(train_loader, best_model, 99.7, device)
    # best_threshold = compute_threshold_v2(validate_loader, best_model, device)
    logger.info(f"Final threshold calculated for testing: {best_threshold:.6f}")

    return best_model, best_threshold

def train(known_normal_data, known_abnormal_data, unknown_data, true_labels, epoch_num, batch_size, model_file=None, lr_rate=3e-4, device='cuda'):
    """
    Main function to train Transformer Autoencoder with Triplet Loss.
    - Uses known normal data, known abnormal data, and unknown data as input.
    - Trains using MSE + Triplet Loss + Known Abnormal Loss.
    - Updates anomaly threshold dynamically per epoch.
    """

    # Merge all data types
    all_data = known_normal_data + known_abnormal_data + unknown_data
    all_labels = [0] * len(known_normal_data) + [4] * len(known_abnormal_data) + [1] * len(unknown_data)
    all_true_labels = true_labels

    # Shuffle data before splitting
    all_data, all_labels, all_true_labels = shuffle(all_data, all_labels, all_true_labels)

    # Train-Validation Split
    train_size = int(0.9 * len(all_data))
    val_size = len(all_data) - train_size

    train_data, val_data = all_data[:train_size], all_data[train_size:]
    train_labels, val_labels = all_labels[:train_size], all_labels[train_size:]
    train_true_labels, val_true_labels = all_true_labels[:train_size], all_true_labels[train_size:]
    
    # Create datasets
    train_dataset = CustomDataset(train_data, train_labels, train_true_labels)
    val_dataset = CustomDataset(val_data, val_labels, val_true_labels)
    
    training_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    validate_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=True)
    
    logger.info(f"Training Samples: {len(train_data)}, Validation Samples: {len(val_data)}")
    logger.info(f"Known Normal Training Samples: {sum(np.array(train_labels) == 0)}, "
               f"Known Abnormal Training Samples: {sum(np.array(train_labels) == 4)}, "  # Add known abnormal stats
               f"Unknown Training Samples: {sum(np.array(train_labels) == 1)}")
    
    # Log number of unknown normal and abnormal samples in training set
    unknown_indices = np.where(np.array(train_labels) == 1)[0]
    unknown_normal_count = sum(np.array(train_true_labels)[unknown_indices] == 0)
    unknown_abnormal_count = sum(np.array(train_true_labels)[unknown_indices] == 1)
    logger.info(f"Unknown Training Samples - Normal: {unknown_normal_count}, Abnormal: {unknown_abnormal_count}")
    
    # Log validation set statistics
    val_known_normal = sum(np.array(val_labels) == 0)
    val_known_abnormal = sum(np.array(val_labels) == 4)  # Add known abnormal stats
    val_unknown = sum(np.array(val_labels) == 1)
    val_unknown_indices = np.where(np.array(val_labels) == 1)[0]
    val_unknown_normal = sum(np.array(val_true_labels)[val_unknown_indices] == 0)
    val_unknown_abnormal = sum(np.array(val_true_labels)[val_unknown_indices] == 1)
    logger.info(f"Validation Set - Known Normal: {val_known_normal}, "
               f"Known Abnormal: {val_known_abnormal}, "  # Add known abnormal stats
               f"Unknown: {val_unknown} (Normal: {val_unknown_normal}, Abnormal: {val_unknown_abnormal})")

    # Train Model
    model, best_threshold = train_generator(training_loader, validate_loader, batch_size, epoch_num, model_file, lr_rate, device)

    return model, best_threshold

def pure_train_generator(train_loader, validate_loader, batch_size, epoch_num, model_file=None, lr_rate=3e-4, device='cuda'):
    """
    Simple training function that only performs reconstruction on known normal data.
    No triplet loss or unknown data separation is performed.
    Mimics the structure and logic of the TF-based train_generator function.
    """
    # Create model
    model = TransformerAE(embed_dim=768, num_heads=12, hidden_dims=[512, 128], \
                          num_layers=2, max_seq_length=20, latent_dim=64).to(device)
    
    # Setup optimizer with learning rate scheduling
    # Similar to the TF optimization with warmup
    num_train_steps = len(train_loader) * epoch_num
    num_warmup_steps = int(0.0001 * num_train_steps)
    
    # Create optimizer
    optimizer = optim.AdamW(model.parameters(), lr=lr_rate)
        
    # Create scheduler similar to TF warmup
    def lr_lambda(current_step):
        if current_step < num_warmup_steps:
            return float(current_step) / float(max(1, num_warmup_steps))
        return max(0.0, float(num_train_steps - current_step) / float(max(1, num_train_steps - num_warmup_steps)))
    
    scheduler = optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)
    # Loss function
    criterion = nn.MSELoss()

    best_val_loss = float('inf')
    best_model = None
    patience = 10
    patience_counter = 0
    
    # Store history similar to TF callbacks
    train_loss_history = []
    val_loss_history = []
    
    # Training Loop with similar structure to TF fit_generator
    for epoch in range(epoch_num):
        model.train()
        total_loss = 0
        batch_count = 0
        
        # Log current learning rate
        current_lr = optimizer.param_groups[0]['lr']
        logger.info(f"Epoch [{epoch+1}/{epoch_num}] - Current learning rate: {current_lr:.6f}")

        for batch in tqdm(train_loader, desc=f"Training Epoch {epoch+1}/{epoch_num}"):
            data, _, _ = batch  # Ignore labels as we're only using normal data
            data = data.to(device)

            optimizer.zero_grad()

            # Forward pass and get reconstruction
            outputs = model(data)
            loss = criterion(outputs, data)
            
            # Backward and optimize
            loss.backward()
            optimizer.step()
            scheduler.step()

            total_loss += loss.item()
            batch_count += 1

        # Calculate average loss
        avg_loss = total_loss / batch_count if batch_count > 0 else float('inf')
        train_loss_history.append(avg_loss)

        # Validation
        model.eval()
        val_loss = 0
        val_batch_count = 0
        
        with torch.no_grad():
            for batch in tqdm(validate_loader, desc="Validating"):
                data, _, _ = batch  # Ignore labels
                data = data.to(device)

                # Calculate reconstruction loss
                outputs = model(data)
                val_batch_loss = criterion(outputs, data)
                
                val_loss += val_batch_loss.item()
                val_batch_count += 1

        val_loss = val_loss / val_batch_count if val_batch_count > 0 else float('inf')
        val_loss_history.append(val_loss)

        logger.info(f"Epoch [{epoch+1}/{epoch_num}] - Training Loss: {avg_loss:.4f}, Validation Loss: {val_loss:.4f}")

        # Early Stopping - similar to TF EarlyStopping callback
        if val_loss < best_val_loss:
            logger.info(f"Validation loss improved from {best_val_loss:.4f} to {val_loss:.4f}")
            best_val_loss = val_loss
            best_model = copy.deepcopy(model)
            patience_counter = 0
            torch.save(model.state_dict(), model_file)
            logger.info(f"Model saved to {model_file}")
        else:
            patience_counter += 1
            logger.info(f"No improvement. Patience {patience_counter}/{patience}")

        if patience_counter >= patience:
            logger.info("Early stopping triggered")
            break

    # Calculate threshold from training data (similar to TF version)
    print("Calculating threshold from training data...")
    best_threshold = compute_threshold(train_loader, best_model, 99.7, device)
    
    # Save loss history and threshold
    threshold_path = model_file.replace('.pth', '_threshold.txt')
    with open(threshold_path, 'w') as f:
        f.write(str(best_threshold))
    
    loss_history_path = model_file.replace('.pth', '_loss_history.pkl')
    with open(loss_history_path, 'wb') as f:
        pickle.dump({'train_loss': train_loss_history, 'val_loss': val_loss_history}, f)

    return best_model, best_threshold

def main_train_pure(known_normal_data, epoch_num, batch_size, model_file=None, lr_rate=3e-4, device='cuda'):
    """
    Main function to train Transformer Autoencoder using only known normal data.
    - Uses pure reconstruction loss (MSE) on known normal data
    - Avoids triplet loss and separation of unknown data
    - Designed for standard autoencoder training workflow
    """
    logger.info("Starting pure reconstruction training with known normal data only")
    
    # Create labels - all data are known normal (label 0)
    all_data = known_normal_data
    all_labels = [0] * len(known_normal_data)
    all_true_labels = [0] * len(known_normal_data)  # All are truly normal

    # Shuffle data before splitting
    all_data, all_labels, all_true_labels = shuffle(all_data, all_labels, all_true_labels)

    # Train-Validation Split
    train_size = int(0.9 * len(all_data))
    train_data, val_data = all_data[:train_size], all_data[train_size:]
    train_labels, val_labels = all_labels[:train_size], all_labels[train_size:]
    train_true_labels, val_true_labels = all_true_labels[:train_size], all_true_labels[train_size:]
    
    # Create datasets
    train_dataset = CustomDataset(train_data, train_labels, train_true_labels)
    val_dataset = CustomDataset(val_data, val_labels, val_true_labels)
    
    # Create data loaders - no need for balanced sampling since all data is normal
    training_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    validate_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=True)

    logger.info(f"Training Samples: {len(train_data)}, Validation Samples: {len(val_data)}")
    
    # Train Model using pure reconstruction approach
    model, best_threshold = pure_train_generator(
        training_loader, validate_loader, batch_size, epoch_num, 
        model_file=model_file, lr_rate=lr_rate, device=device
    )

    return model, best_threshold