import torch
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
import numpy as np
from tqdm import tqdm
import logging
import copy
from .models import TripletLossLatent, TransformerWithClassifierHead
from .evaluation import compute_threshold_v2, compute_threshold
from sklearn.utils import shuffle
from .data import CustomDataset

logger = logging.getLogger(__name__)

class TripletDataset(Dataset):
    """
    Custom dataset for triplet training with fixed anchor+positive pairs and cycling through negatives.
    Each batch will contain:
    - Fixed anchor (known normal)
    - Fixed positive (known normal)
    - Cycling negative (known abnormal)
    """
    def __init__(self, known_normal_data, known_abnormal_data):
        # Convert lists to numpy arrays for better performance
        self.known_normal_data = np.array(known_normal_data, dtype=np.float32)
        self.known_abnormal_data = np.array(known_abnormal_data, dtype=np.float32)
        self.normal_len = len(known_normal_data)
        self.abnormal_len = len(known_abnormal_data)
        
        # Create fixed pairs of anchors and positives from normal data
        self.anchor_indices = np.arange(self.normal_len)
        self.positive_indices = np.roll(self.anchor_indices, 1)  # Shift by 1 to create pairs
        
    def __len__(self):
        return self.normal_len  # One triplet per normal sample
    
    def __getitem__(self, idx):
        # Get anchor and positive from normal data
        anchor = torch.from_numpy(self.known_normal_data[self.anchor_indices[idx]])
        positive = torch.from_numpy(self.known_normal_data[self.positive_indices[idx]])
        
        # Cycle through abnormal samples as negatives
        negative_idx = idx % self.abnormal_len
        negative = torch.from_numpy(self.known_abnormal_data[negative_idx])
        
        return anchor, positive, negative

def train_classifier_head_with_triplet(known_normal_data, known_abnormal_data, unknown_data, true_labels, 
                                     epoch_num, batch_size, model_file=None, lr_rate=3e-4, device='cuda',
                                     triplet_weight=0.4, recon_weight=0.4, classifier_weight=0.2,
                                     patience=10):
    """
    Train the model using:
    1. Triplet loss on latent space (both known normal and known abnormal)
    2. Reconstruction error (on known normal only)
    3. Classifier head for binary classification
    
    Args:
        known_normal_data: List of known normal samples
        known_abnormal_data: List of known abnormal samples
        unknown_data: List of unknown samples
        true_labels: Ground truth labels
        epoch_num: Number of epochs
        batch_size: Batch size
        model_file: Path to save the model
        lr_rate: Learning rate
        device: Device to run on
        triplet_weight: Weight for triplet loss in total loss
        recon_weight: Weight for reconstruction loss in total loss
        classifier_weight: Weight for classifier loss in total loss
        patience: Number of epochs to wait before early stopping
    """
    # ====================
    # Model Setup
    # ====================
    model = TransformerWithClassifierHead(
        embed_dim=768, num_heads=12, hidden_dims=[512, 128], 
        latent_dim=64, num_layers=2, max_seq_length=20, 
        classifier_dims=[8]
    ).to(device)
    
    # Setup optimizer with learning rate scheduling
    num_train_steps = len(known_normal_data) * epoch_num
    num_warmup_steps = int(0.0001 * num_train_steps)
    
    optimizer = torch.optim.AdamW(model.parameters(), lr=lr_rate)
    
    def lr_lambda(current_step):
        if current_step < num_warmup_steps:
            return float(current_step) / float(max(1, num_warmup_steps))
        return max(0.0, float(num_train_steps - current_step) / float(max(1, num_train_steps - num_warmup_steps)))
    
    scheduler = torch.optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)
    
    # Loss functions
    triplet_loss_fn = TripletLossLatent(margin=1.0)
    reconstruction_loss_fn = nn.MSELoss()
    classifier_loss_fn = nn.BCELoss()
    
    # ====================
    # Data Preparation
    # ====================
    # Split known normal data into train and validation
    train_size = int(0.9 * len(known_normal_data))
    val_size = len(known_normal_data) - train_size

    train_known_normal = known_normal_data[:train_size]
    val_known_normal = known_normal_data[train_size:]

    # Split known abnormal data into train and validation
    train_size_abnormal = int(0.9 * len(known_abnormal_data))
    val_size_abnormal = len(known_abnormal_data) - train_size_abnormal

    train_known_abnormal = known_abnormal_data[:train_size_abnormal]
    val_known_abnormal = known_abnormal_data[train_size_abnormal:]

    # Split unknown data into train and validation
    train_size_unknown = int(0.9 * len(unknown_data))
    val_size_unknown = len(unknown_data) - train_size_unknown

    train_unknown = unknown_data[:train_size_unknown]
    val_unknown = unknown_data[train_size_unknown:]

    # Create datasets and dataloaders
    train_dataset = TripletDataset(train_known_normal, train_known_abnormal)
    val_dataset = TripletDataset(val_known_normal, val_known_abnormal)

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)

    logger.info(f"Training Samples - Known Normal: {len(train_known_normal)}, Known Abnormal: {len(train_known_abnormal)}")
    logger.info(f"Validation Samples - Known Normal: {len(val_known_normal)}, Known Abnormal: {len(val_known_abnormal)}")

    # ====================
    # Training Loop
    # ====================
    best_val_loss = float('inf')
    best_model = None
    patience_counter = 0
    best_epoch = 0
    
    logger.info(f"Starting training with patience={patience} for early stopping")
    
    for epoch in range(epoch_num):
        # ====================
        # Training Phase
        # ====================
        model.train()
        total_loss = 0
        total_triplet_loss = 0
        total_recon_loss = 0
        total_classifier_loss = 0
        
        for batch_idx, (anchors, positives, negatives) in enumerate(tqdm(train_loader, desc=f"Epoch {epoch+1}")):
            # Move data to device
            anchors = anchors.to(device)
            positives = positives.to(device)
            negatives = negatives.to(device)
            
            # Forward pass
            anchor_recon, anchor_latent, anchor_class = model(anchors, return_latent=True, return_classifier=True)
            positive_recon, positive_latent, positive_class = model(positives, return_latent=True, return_classifier=True)
            negative_recon, negative_latent, negative_class = model(negatives, return_latent=True, return_classifier=True)
            
            # Calculate individual losses
            triplet_loss = triplet_loss_fn(anchor_latent, positive_latent, negative_latent)
            recon_loss = (reconstruction_loss_fn(anchor_recon, anchors) + 
                         reconstruction_loss_fn(positive_recon, positives)) / 2
            
            normal_targets = torch.zeros(anchors.size(0), 1, device=device)
            abnormal_targets = torch.ones(negatives.size(0), 1, device=device)
            
            classifier_loss = (classifier_loss_fn(anchor_class, normal_targets) +
                             classifier_loss_fn(positive_class, normal_targets) +
                             classifier_loss_fn(negative_class, abnormal_targets)) / 3
            
            # Combined loss with weights
            loss = (triplet_weight * triplet_loss + 
                   recon_weight * recon_loss + 
                   classifier_weight * classifier_loss)
            
            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            scheduler.step()
            
            # Accumulate losses
            total_loss += loss.item()
            total_triplet_loss += triplet_loss.item()
            total_recon_loss += recon_loss.item()
            total_classifier_loss += classifier_loss.item()
        
        # Calculate average training losses
        avg_loss = total_loss / len(train_loader)
        avg_triplet_loss = total_triplet_loss / len(train_loader)
        avg_recon_loss = total_recon_loss / len(train_loader)
        avg_classifier_loss = total_classifier_loss / len(train_loader)
        
        # ====================
        # Validation Phase
        # ====================
        model.eval()
        val_total_loss = 0
        val_triplet_loss = 0
        val_recon_loss = 0
        val_classifier_loss = 0
        
        with torch.no_grad():
            for batch_idx, (anchors, positives, negatives) in enumerate(tqdm(val_loader, desc=f"Validation")):
                # Move data to device
                anchors = anchors.to(device)
                positives = positives.to(device)
                negatives = negatives.to(device)
                
                # Forward pass
                anchor_recon, anchor_latent, anchor_class = model(anchors, return_latent=True, return_classifier=True)
                positive_recon, positive_latent, positive_class = model(positives, return_latent=True, return_classifier=True)
                negative_recon, negative_latent, negative_class = model(negatives, return_latent=True, return_classifier=True)
                
                # Calculate individual validation losses
                triplet_loss = triplet_loss_fn(anchor_latent, positive_latent, negative_latent)
                recon_loss = (reconstruction_loss_fn(anchor_recon, anchors) + 
                            reconstruction_loss_fn(positive_recon, positives)) / 2
                
                normal_targets = torch.zeros(anchors.size(0), 1, device=device)
                abnormal_targets = torch.ones(negatives.size(0), 1, device=device)
                
                classifier_loss = (classifier_loss_fn(anchor_class, normal_targets) +
                                 classifier_loss_fn(positive_class, normal_targets) +
                                 classifier_loss_fn(negative_class, abnormal_targets)) / 3
                
                # Combined validation loss with weights
                val_loss = (triplet_weight * triplet_loss + 
                          recon_weight * recon_loss + 
                          classifier_weight * classifier_loss)
                
                # Accumulate losses
                val_total_loss += val_loss.item()
                val_triplet_loss += triplet_loss.item()
                val_recon_loss += recon_loss.item()
                val_classifier_loss += classifier_loss.item()
        
        # Calculate average validation losses
        avg_val_loss = val_total_loss / len(val_loader)
        avg_val_triplet_loss = val_triplet_loss / len(val_loader)
        avg_val_recon_loss = val_recon_loss / len(val_loader)
        avg_val_classifier_loss = val_classifier_loss / len(val_loader)
        
        # Log both training and validation information in one line
        logger.info(f"Epoch {epoch+1}/{epoch_num} - Train: Total={avg_loss:.4f}, Triplet={avg_triplet_loss:.4f}, "
                   f"Recon={avg_recon_loss:.4f}, Classifier={avg_classifier_loss:.4f} | "
                   f"Val: Total={avg_val_loss:.4f}, Triplet={avg_val_triplet_loss:.4f}, "
                   f"Recon={avg_val_recon_loss:.4f}, Classifier={avg_val_classifier_loss:.4f}")
        
        # ====================
        # Model Saving and Early Stopping
        # ====================
        if avg_val_loss < best_val_loss:
            improvement = best_val_loss - avg_val_loss
            logger.info(f"Model improved: {best_val_loss:.6f} -> {avg_val_loss:.6f} (improvement: {improvement:.6f})")
            best_val_loss = avg_val_loss
            best_model = copy.deepcopy(model)
            best_epoch = epoch + 1
            patience_counter = 0
            if model_file:
                torch.save(best_model.state_dict(), model_file)
                logger.info(f"Saved best model to {model_file}")
        else:
            patience_counter += 1
            logger.info(f"No improvement for {patience_counter}/{patience} epochs")
            if patience_counter >= patience:
                logger.info(f"Early stopping triggered after {epoch+1} epochs")
                logger.info(f"Best model was at epoch {best_epoch} with validation loss {best_val_loss:.6f}")
                break
    
    # Load the best model
    # if best_model is not None:
    #     model.load_state_dict(best_model)
    #     logger.info(f"Loaded best model from epoch {best_epoch}")
    
    # Create a dataset with only known normal data for threshold calculation
    threshold_dataset = CustomDataset(known_normal_data, [0]*len(known_normal_data), [0]*len(known_normal_data))
    threshold_loader = DataLoader(threshold_dataset, batch_size=batch_size, shuffle=False)
    
    # Compute optimal threshold using only known normal data
    best_threshold = compute_threshold(threshold_loader, best_model, percent=99.7, device=device)
    
    return best_model, best_threshold

def train_classifier_head_with_triplet_gaining(known_normal_data, known_abnormal_data, unknown_data, true_labels, 
                                     epoch_num, batch_size, model_file=None, lr_rate=3e-4, device='cuda',
                                     triplet_weight=0.4, recon_weight=0.4, classifier_weight=0.2,
                                     patience=10, gaining_patience=3):
    """
    Train the model using:
    1. Triplet loss on latent space (both known normal and known abnormal)
    2. Reconstruction error (on known normal only)
    3. Classifier head for binary classification
    4. Dynamically gain abnormal samples from unknown data using a threshold calculated from training data
    
    Args:
        known_normal_data: List of known normal samples
        known_abnormal_data: List of known abnormal samples
        unknown_data: List of unknown samples
        true_labels: Ground truth labels of unknown data
        epoch_num: Number of epochs
        batch_size: Batch size
        model_file: Path to save the model
        lr_rate: Learning rate
        device: Device to run on
        triplet_weight: Weight for triplet loss in total loss
        recon_weight: Weight for reconstruction loss in total loss
        classifier_weight: Weight for classifier loss in total loss
        patience: Number of epochs to wait before trying to gain new samples
        gaining_patience: Number of consecutive gaining attempts without new samples before early stopping
    """
    # Check if true_labels is provided and not None
    has_true_labels = true_labels is not None and len(true_labels) > 0

    # ====================
    # Model Setup
    # ====================
    model = TransformerWithClassifierHead(
        embed_dim=768, num_heads=12, hidden_dims=[512, 128], 
        latent_dim=64, num_layers=2, max_seq_length=20, 
        classifier_dims=[8]
    ).to(device)
    
    # Setup optimizer with learning rate scheduling
    num_train_steps = len(known_normal_data) * epoch_num
    num_warmup_steps = int(0.0001 * num_train_steps)
    
    optimizer = torch.optim.AdamW(model.parameters(), lr=lr_rate)
    
    def lr_lambda(current_step):
        if current_step < num_warmup_steps:
            return float(current_step) / float(max(1, num_warmup_steps))
        return max(0.0, float(num_train_steps - current_step) / float(max(1, num_train_steps - num_warmup_steps)))
    
    scheduler = torch.optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)
    
    # Loss functions
    triplet_loss_fn = TripletLossLatent(margin=1.0)
    reconstruction_loss_fn = nn.MSELoss()
    classifier_loss_fn = nn.BCELoss()
    
    # ====================
    # Data Preparation
    # ====================
    # Split known normal data into train and validation
    train_size = int(0.9 * len(known_normal_data))
    val_size = len(known_normal_data) - train_size

    train_known_normal = known_normal_data[:train_size]
    val_known_normal = known_normal_data[train_size:]

    # Split known abnormal data into train and validation
    train_size_abnormal = int(0.9 * len(known_abnormal_data))
    val_size_abnormal = len(known_abnormal_data) - train_size_abnormal

    train_known_abnormal = known_abnormal_data[:train_size_abnormal]
    val_known_abnormal = known_abnormal_data[train_size_abnormal:]

    # Split unknown data into train and validation
    train_size_unknown = int(0.9 * len(unknown_data))
    val_size_unknown = len(unknown_data) - train_size_unknown

    train_unknown = unknown_data[:train_size_unknown]
    val_unknown = unknown_data[train_size_unknown:]
    
    # Create initial datasets and dataloaders
    train_dataset = TripletDataset(train_known_normal, train_known_abnormal)
    val_dataset = TripletDataset(val_known_normal, val_known_abnormal)

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)

    logger.info(f"Training Samples - Known Normal: {len(train_known_normal)}, Known Abnormal: {len(train_known_abnormal)}")
    logger.info(f"Validation Samples - Known Normal: {len(val_known_normal)}, Known Abnormal: {len(val_known_abnormal)}")
    logger.info(f"Unknown Samples - Train: {len(train_unknown)}, Val: {len(val_unknown)}")

    # ====================
    # Training Loop
    # ====================
    best_val_loss = float('inf')
    best_model = None
    patience_counter = 0
    gaining_patience_counter = 0
    best_epoch = 0
    
    logger.info(f"Starting training with patience={patience} for gaining samples and gaining_patience={gaining_patience} for early stopping")
    
    for epoch in range(epoch_num):
        # ====================
        # Training Phase
        # ====================
        model.train()
        total_loss = 0
        total_triplet_loss = 0
        total_recon_loss = 0
        total_classifier_loss = 0
        
        for batch_idx, (anchors, positives, negatives) in enumerate(tqdm(train_loader, desc=f"Epoch {epoch+1}")):
            # Move data to device
            anchors = anchors.to(device)
            positives = positives.to(device)
            negatives = negatives.to(device)
            
            # Forward pass
            anchor_recon, anchor_latent, anchor_class = model(anchors, return_latent=True, return_classifier=True)
            positive_recon, positive_latent, positive_class = model(positives, return_latent=True, return_classifier=True)
            negative_recon, negative_latent, negative_class = model(negatives, return_latent=True, return_classifier=True)
            
            # Calculate individual losses
            triplet_loss = triplet_loss_fn(anchor_latent, positive_latent, negative_latent)
            recon_loss = (reconstruction_loss_fn(anchor_recon, anchors) + 
                         reconstruction_loss_fn(positive_recon, positives)) / 2
            
            normal_targets = torch.zeros(anchors.size(0), 1, device=device)
            abnormal_targets = torch.ones(negatives.size(0), 1, device=device)
            
            classifier_loss = (classifier_loss_fn(anchor_class, normal_targets) +
                             classifier_loss_fn(positive_class, normal_targets) +
                             classifier_loss_fn(negative_class, abnormal_targets)) / 3
            
            # Combined loss with weights
            loss = (triplet_weight * triplet_loss + 
                   recon_weight * recon_loss + 
                   classifier_weight * classifier_loss)
            
            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            scheduler.step()
            
            # Accumulate losses
            total_loss += loss.item()
            total_triplet_loss += triplet_loss.item()
            total_recon_loss += recon_loss.item()
            total_classifier_loss += classifier_loss.item()
        
        # Calculate average training losses
        avg_loss = total_loss / len(train_loader)
        avg_triplet_loss = total_triplet_loss / len(train_loader)
        avg_recon_loss = total_recon_loss / len(train_loader)
        avg_classifier_loss = total_classifier_loss / len(train_loader)
        
        # ====================
        # Validation Phase
        # ====================
        model.eval()
        val_total_loss = 0
        val_triplet_loss = 0
        val_recon_loss = 0
        val_classifier_loss = 0
        
        with torch.no_grad():
            for batch_idx, (anchors, positives, negatives) in enumerate(tqdm(val_loader, desc=f"Validation")):
                # Move data to device
                anchors = anchors.to(device)
                positives = positives.to(device)
                negatives = negatives.to(device)
                
                # Forward pass
                anchor_recon, anchor_latent, anchor_class = model(anchors, return_latent=True, return_classifier=True)
                positive_recon, positive_latent, positive_class = model(positives, return_latent=True, return_classifier=True)
                negative_recon, negative_latent, negative_class = model(negatives, return_latent=True, return_classifier=True)
                
                # Calculate individual validation losses
                triplet_loss = triplet_loss_fn(anchor_latent, positive_latent, negative_latent)
                recon_loss = (reconstruction_loss_fn(anchor_recon, anchors) + 
                            reconstruction_loss_fn(positive_recon, positives)) / 2
                
                normal_targets = torch.zeros(anchors.size(0), 1, device=device)
                abnormal_targets = torch.ones(negatives.size(0), 1, device=device)
                
                classifier_loss = (classifier_loss_fn(anchor_class, normal_targets) +
                                 classifier_loss_fn(positive_class, normal_targets) +
                                 classifier_loss_fn(negative_class, abnormal_targets)) / 3
                
                # Combined validation loss with weights
                val_loss = (triplet_weight * triplet_loss + 
                          recon_weight * recon_loss + 
                          classifier_weight * classifier_loss)
                
                # Accumulate losses
                val_total_loss += val_loss.item()
                val_triplet_loss += triplet_loss.item()
                val_recon_loss += recon_loss.item()
                val_classifier_loss += classifier_loss.item()
        
        # Calculate average validation losses
        avg_val_loss = val_total_loss / len(val_loader)
        avg_val_triplet_loss = val_triplet_loss / len(val_loader)
        avg_val_recon_loss = val_recon_loss / len(val_loader)
        avg_val_classifier_loss = val_classifier_loss / len(val_loader)
        
        # Log both training and validation information in one line
        logger.info(f"Epoch {epoch+1}/{epoch_num} - Train: Total={avg_loss:.4f}, Triplet={avg_triplet_loss:.4f}, "
                   f"Recon={avg_recon_loss:.4f}, Classifier={avg_classifier_loss:.4f} | "
                   f"Val: Total={avg_val_loss:.4f}, Triplet={avg_val_triplet_loss:.4f}, "
                   f"Recon={avg_val_recon_loss:.4f}, Classifier={avg_val_classifier_loss:.4f}")
        
        # ====================
        # Model Saving and Early Stopping
        # ====================
        if avg_val_loss < best_val_loss:
            improvement = best_val_loss - avg_val_loss
            logger.info(f"Model improved: {best_val_loss:.6f} -> {avg_val_loss:.6f} (improvement: {improvement:.6f})")
            best_val_loss = avg_val_loss
            best_model = model.state_dict().copy()
            best_epoch = epoch + 1
            patience_counter = 0
            if model_file:
                torch.save(model.state_dict(), model_file)
                logger.info(f"Saved best model to {model_file}")
        else:
            patience_counter += 1
            logger.info(f"No improvement for {patience_counter}/{patience} epochs")
            
            # ====================
            # Gaining Abnormal Samples from Training and Validation Unknown Data
            # ====================
            # if True:
            if patience_counter >= patience and (len(train_unknown) > 0 or len(val_unknown) > 0):
                logger.info(f"No improvement for {patience} epochs. Attempting to gain new abnormal samples...")
                
                # First, calculate the classifier threshold using training data
                logger.info("Calculating classifier threshold from training data...")
                train_normal_dataset = CustomDataset(train_known_normal, [0]*len(train_known_normal), [0]*len(train_known_normal))
                train_normal_loader = DataLoader(train_normal_dataset, batch_size=batch_size, shuffle=False)
                
                # Get classifier scores for all known normal training data
                model.eval()
                train_normal_scores = []
                
                with torch.no_grad():
                    for data, _, _ in train_normal_loader:
                        data = data.to(device)
                        _, _, classification = model(data, return_latent=True, return_classifier=True)
                        scores = classification.squeeze().cpu().numpy()
                        train_normal_scores.extend(scores)
                
                # Calculate threshold where 99.7% of normal data is below (3-sigma rule)
                classifier_threshold = np.percentile(train_normal_scores, 99.7)
                logger.info(f"Calculated classifier threshold: {classifier_threshold:.6f}")
                
                # Evaluate model on unknown data before gaining (if true labels are available)
                if has_true_labels:
                    logger.info("Evaluating model performance on unknown data before gaining...")
                    
                    # Evaluate on training unknown data
                    if len(train_unknown) > 0:
                        train_unknown_true_labels_subset = true_labels[:len(train_unknown)]
                        train_unknown_dataset = CustomDataset(train_unknown, [0]*len(train_unknown), train_unknown_true_labels_subset)
                        train_unknown_loader = DataLoader(train_unknown_dataset, batch_size=batch_size, shuffle=False)
                        
                        model.eval()
                        train_unknown_scores = []
                        train_unknown_true_labels = []
                        
                        with torch.no_grad():
                            for data, _, true_label in train_unknown_loader:
                                data = data.to(device)
                                _, _, classification = model(data, return_latent=True, return_classifier=True)
                                scores = classification.squeeze().cpu().numpy()
                                train_unknown_scores.extend(scores)
                                train_unknown_true_labels.extend(true_label.numpy())
                        
                        # Calculate metrics
                        from sklearn.metrics import roc_curve, auc, precision_recall_fscore_support
                        
                        # ROC curve and AUC
                        fpr, tpr, _ = roc_curve(train_unknown_true_labels, train_unknown_scores, pos_label=1)
                        train_auc = auc(fpr, tpr)
                        
                        # Binary predictions using the calculated threshold
                        train_predictions = (np.array(train_unknown_scores) >= classifier_threshold).astype(int)
                        train_precision, train_recall, train_f1, _ = precision_recall_fscore_support(
                            train_unknown_true_labels, train_predictions, average='binary', pos_label=1
                        )
                        
                        logger.info(f"Pre-Gaining Training Unknown Data Metrics (threshold={classifier_threshold:.6f}) - AUC: {train_auc:.4f}, "
                                   f"Precision: {train_precision:.4f}, Recall: {train_recall:.4f}, F1: {train_f1:.4f}")
                    
                    # Evaluate on validation unknown data
                    if len(val_unknown) > 0:
                        val_unknown_true_labels_subset = true_labels[len(train_unknown):len(train_unknown)+len(val_unknown)]
                        val_unknown_dataset = CustomDataset(val_unknown, [0]*len(val_unknown), val_unknown_true_labels_subset)
                        val_unknown_loader = DataLoader(val_unknown_dataset, batch_size=batch_size, shuffle=False)
                        
                        model.eval()
                        val_unknown_scores = []
                        val_unknown_true_labels = []
                        
                        with torch.no_grad():
                            for data, _, true_label in val_unknown_loader:
                                data = data.to(device)
                                _, _, classification = model(data, return_latent=True, return_classifier=True)
                                scores = classification.squeeze().cpu().numpy()
                                val_unknown_scores.extend(scores)
                                val_unknown_true_labels.extend(true_label.numpy())
                        
                        # Calculate metrics
                        # ROC curve and AUC
                        fpr, tpr, _ = roc_curve(val_unknown_true_labels, val_unknown_scores, pos_label=1)
                        val_auc = auc(fpr, tpr)
                        
                        # Binary predictions using the calculated threshold
                        val_predictions = (np.array(val_unknown_scores) >= classifier_threshold).astype(int)
                        val_precision, val_recall, val_f1, _ = precision_recall_fscore_support(
                            val_unknown_true_labels, val_predictions, average='binary', pos_label=1
                        )
                        
                        logger.info(f"Pre-Gaining Validation Unknown Data Metrics (threshold={classifier_threshold:.6f}) - AUC: {val_auc:.4f}, "
                                   f"Precision: {val_precision:.4f}, Recall: {val_recall:.4f}, F1: {val_f1:.4f}")
                
                # Process training unknown data
                if len(train_unknown) > 0:
                    logger.info(f"Processing {len(train_unknown)} training unknown samples...")
                    
                    # Create dataset for training unknown samples
                    train_unknown_dataset = CustomDataset(train_unknown, [0]*len(train_unknown), [0]*len(train_unknown))
                    train_unknown_loader = DataLoader(train_unknown_dataset, batch_size=batch_size, shuffle=False)
                    
                    # Evaluate training unknown samples with classifier head
                    model.eval()
                    train_unknown_scores = []
                    train_unknown_indices = []
                    
                    with torch.no_grad():
                        for i, (data, _, _) in enumerate(train_unknown_loader):
                            data = data.to(device)
                            _, _, classification = model(data, return_latent=True, return_classifier=True)
                            scores = classification.squeeze().cpu().numpy()

                            # Store scores and corresponding indices
                            for j, score in enumerate(scores):
                                if i * batch_size + j < len(train_unknown):
                                    train_unknown_scores.append(score)
                                    train_unknown_indices.append(i * batch_size + j)
                    
                    # Find top 1% samples with highest classifier scores
                    if train_unknown_scores:
                        # Sort indices by scores in descending order
                        sorted_indices = [idx for _, idx in sorted(zip(train_unknown_scores, train_unknown_indices), 
                                                                 key=lambda pair: pair[0], reverse=True)]
                        
                        # Take top 1% as high confidence abnormal samples
                        top_percent = max(int(0.01 * len(sorted_indices)), 1)
                        train_high_confidence_indices = sorted_indices[:top_percent]
                    else:
                        train_high_confidence_indices = None
                    
                    if train_high_confidence_indices:
                        # Get the corresponding samples
                        train_new_abnormal_samples = [train_unknown[idx] for idx in train_high_confidence_indices]
                        
                        # Calculate accuracy of gained samples if true labels are available
                        if true_labels is not None:
                            # Get true labels for train_unknown
                            train_unknown_true_labels = true_labels[:len(train_unknown)]
                            
                            # Calculate accuracy of the gained samples
                            train_true_abnormal_count = sum(1 for idx in train_high_confidence_indices 
                                                    if idx < len(train_unknown_true_labels) and 
                                                    train_unknown_true_labels[idx] == 1)
                            
                            train_accuracy = train_true_abnormal_count / len(train_high_confidence_indices) if train_high_confidence_indices else 0
                            logger.info(f"Gained {len(train_new_abnormal_samples)} new abnormal samples from training data with accuracy: {train_accuracy:.4f}")
                            logger.info(f"True abnormal: {train_true_abnormal_count}/{len(train_high_confidence_indices)}")
                        else:
                            logger.info(f"Gained {len(train_new_abnormal_samples)} new abnormal samples from training data")
                        
                        # Add to known abnormal data
                        train_known_abnormal.extend(train_new_abnormal_samples)
                        
                        # Remove from unknown data
                        # Sort indices in descending order to avoid index shifting when removing
                        train_high_confidence_indices.sort(reverse=True)
                        for idx in train_high_confidence_indices:
                            train_unknown.pop(idx)
                    else:
                        logger.info("No new abnormal samples found in training unknown data")
                
                # Process validation unknown data
                if len(val_unknown) > 0:
                    logger.info(f"Processing {len(val_unknown)} validation unknown samples...")
                    
                    # Create dataset for validation unknown samples
                    val_unknown_dataset = CustomDataset(val_unknown, [0]*len(val_unknown), [0]*len(val_unknown))
                    val_unknown_loader = DataLoader(val_unknown_dataset, batch_size=batch_size, shuffle=False)
                    
                    # Evaluate validation unknown samples with classifier head
                    model.eval()
                    val_unknown_scores = []
                    val_unknown_indices = []
                    
                    with torch.no_grad():
                        for i, (data, _, _) in enumerate(val_unknown_loader):
                            data = data.to(device)
                            _, _, classification = model(data, return_latent=True, return_classifier=True)
                            scores = classification.squeeze().cpu().numpy()

                            # Store scores and corresponding indices
                            for j, score in enumerate(scores):
                                if i * batch_size + j < len(val_unknown):
                                    val_unknown_scores.append(score)
                                    val_unknown_indices.append(i * batch_size + j)
                    
                    # Find top 1% samples with highest classifier scores
                    if val_unknown_scores:
                        # Sort indices by scores in descending order
                        sorted_indices = [idx for _, idx in sorted(zip(val_unknown_scores, val_unknown_indices), 
                                                                 key=lambda pair: pair[0], reverse=True)]
                        
                        # Take top 1% as high confidence abnormal samples
                        top_percent = max(int(0.01 * len(sorted_indices)), 1)
                        val_high_confidence_indices = sorted_indices[:top_percent]
                    else:
                        val_high_confidence_indices = None
                    
                    if val_high_confidence_indices:
                        # Get the corresponding samples
                        val_new_abnormal_samples = [val_unknown[idx] for idx in val_high_confidence_indices]
                        
                        # Calculate accuracy of gained samples if true labels are available
                        if true_labels is not None:
                            # Get true labels for val_unknown
                            val_unknown_true_labels = true_labels[len(train_unknown):len(train_unknown)+len(val_unknown)]
                            
                            # Calculate accuracy of the gained samples
                            val_true_abnormal_count = sum(1 for idx in val_high_confidence_indices 
                                                    if idx < len(val_unknown_true_labels) and 
                                                    val_unknown_true_labels[idx] == 1)
                            
                            val_accuracy = val_true_abnormal_count / len(val_high_confidence_indices) if val_high_confidence_indices else 0
                            logger.info(f"Gained {len(val_new_abnormal_samples)} new abnormal samples from validation data with accuracy: {val_accuracy:.4f}")
                            logger.info(f"True abnormal: {val_true_abnormal_count}/{len(val_high_confidence_indices)}")
                        else:
                            logger.info(f"Gained {len(val_new_abnormal_samples)} new abnormal samples from validation data")
                        
                        # Add to known abnormal data for validation
                        val_known_abnormal.extend(val_new_abnormal_samples)
                        
                        # Remove from unknown data
                        # Sort indices in descending order to avoid index shifting when removing
                        val_high_confidence_indices.sort(reverse=True)
                        for idx in val_high_confidence_indices:
                            val_unknown.pop(idx)
                    else:
                        logger.info("No new abnormal samples found in validation unknown data")
                
                # Check if we gained any new samples from either training or validation data
                total_gained = (train_high_confidence_indices if 'train_high_confidence_indices' in locals() else []) + \
                              (val_high_confidence_indices if 'val_high_confidence_indices' in locals() else [])
                
                if total_gained:
                    # Create new datasets with updated data
                    train_dataset = TripletDataset(train_known_normal, train_known_abnormal)
                    val_dataset = TripletDataset(val_known_normal, val_known_abnormal)
                    
                    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
                    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
                    
                    logger.info(f"Updated Training Samples - Known Normal: {len(train_known_normal)}, Known Abnormal: {len(train_known_abnormal)}")
                    logger.info(f"Updated Validation Samples - Known Normal: {len(val_known_normal)}, Known Abnormal: {len(val_known_abnormal)}")
                    logger.info(f"Remaining Unknown Samples - Training: {len(train_unknown)}, Validation: {len(val_unknown)}")
                    
                    # Reset patience counter and best validation loss
                    patience_counter = 0
                    gaining_patience_counter = 0
                    best_val_loss = float('inf')  # Reset best validation loss
                    
                    logger.info("Resetting best validation loss to infinity and continuing training with newly gained samples...")
                else:
                    # No new samples found
                    gaining_patience_counter += 1
                    logger.info(f"No new abnormal samples found. Gaining patience: {gaining_patience_counter}/{gaining_patience}")
                    
                    if gaining_patience_counter >= gaining_patience:
                        logger.info(f"No new samples gained for {gaining_patience} consecutive attempts. Early stopping.")
                        break
                    else:
                        # Reset patience counter to continue training
                        patience_counter = 0
                        logger.info("Resetting patience counter and continuing training...")
        
        
        
        # Check if we've reached the maximum number of epochs
        if epoch == epoch_num - 1:
            logger.info(f"Reached maximum number of epochs ({epoch_num})")
    
    # Load the best model
    if best_model is not None:
        model.load_state_dict(best_model)
        logger.info(f"Loaded best model from epoch {best_epoch}")
    
    # Create a dataset with only known normal data for threshold calculation
    threshold_dataset = CustomDataset(known_normal_data, [0]*len(known_normal_data), [0]*len(known_normal_data))
    threshold_loader = DataLoader(threshold_dataset, batch_size=batch_size, shuffle=False)
    
    # Compute optimal threshold using only known normal data
    best_threshold = compute_threshold(threshold_loader, model, percent=99.7, device=device)
    
    return model, best_threshold
