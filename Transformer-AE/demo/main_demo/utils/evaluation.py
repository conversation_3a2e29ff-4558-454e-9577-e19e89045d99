import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from tqdm import tqdm
import logging
from sklearn.metrics import roc_curve, auc, precision_recall_fscore_support
from .models import TransformerWithClassifierHead

logger = logging.getLogger(__name__)

def compute_threshold(train_loader, model, percent, device):
    """
    Computes the anomaly threshold using the 3-sigma rule.
    """
    logger.info("Computing Anomaly Threshold...")
    model.to(device)
    model.eval()
    reconstruction_errors = []
    with torch.no_grad():
        for batch in tqdm(train_loader, desc="Computing reconstruction errors"):
            data, labels, _ = batch
            known_normal_data = data[labels == 0].to(device)
            if len(known_normal_data) == 0:
                continue
                
            # Handle different model types
            if isinstance(model, TransformerWithClassifierHead):
                reconstructed, _ = model(known_normal_data, return_latent=True, return_classifier=False)
            else:
                reconstructed, _ = model(known_normal_data, return_latent=True)
                
            error = torch.mean((known_normal_data - reconstructed) ** 2, dim=2).mean(dim=1)
            reconstruction_errors.extend(error.cpu().numpy())

    # Calculate threshold as the 95th percentile of reconstruction errors
    threshold = np.percentile(reconstruction_errors, percent)

    logger.info(f"Using {percent} percentile threshold: {threshold:.6f}")
    # Log how many samples are above/below threshold
    above_threshold = sum(e >= threshold for e in reconstruction_errors)
    below_threshold = sum(e < threshold for e in reconstruction_errors)
    total_samples = len(reconstruction_errors)
    
    logger.info(f"Threshold Statistics: {below_threshold}/{total_samples} ({100*below_threshold/total_samples:.2f}%) samples below threshold")
    logger.info(f"Threshold Statistics: {above_threshold}/{total_samples} ({100*above_threshold/total_samples:.2f}%) samples above threshold")
    return threshold

def compute_threshold_v2(val_loader, model, device, target_metric='f1', pos_label=1, training_option='standard'):
    """
    Computes the optimal anomaly threshold based on a validation set with both normal and anomalous samples.
    
    Args:
        val_loader: DataLoader with validation samples (both normal and anomalous)
        model: Trained autoencoder model
        device: Device to run the model on
        target_metric: Metric to optimize ('precision', 'recall', 'f1')
        pos_label: The label to consider as positive class (typically 0 for normal samples)
        training_option: Training mode option ('standard' or 'triplet')
        
    Returns:
        optimal_threshold: The threshold that maximizes the target metric
    """
    logger.info(f"Computing optimal threshold on validation set (optimizing {target_metric})...")
    model.eval()
    
    # Collect reconstruction errors and true labels
    reconstruction_errors = []
    true_labels = []
    
    with torch.no_grad():
        for batch in tqdm(val_loader, desc="Computing reconstruction errors"):
            if training_option == 'triplet':
                # For TripletDataset, batch contains (anchor, positive, negative)
                anchor_data, positive_data, negative_data = batch
                
                # Process anchor and positive samples as normal data
                normal_data = torch.cat([anchor_data, positive_data], dim=0).to(device)
                normal_labels = torch.zeros(len(normal_data), device=device)
                
                # Process negative samples as abnormal data
                abnormal_data = negative_data.to(device)
                abnormal_labels = torch.ones(len(abnormal_data), device=device)
                
                # Combine all data and labels
                data = torch.cat([normal_data, abnormal_data], dim=0)
                labels = torch.cat([normal_labels, abnormal_labels], dim=0)
            else:
                # For standard dataset
                data, labels, true_lbls = batch
                data = data.to(device)
                labels = labels.to(device)
                true_lbls = true_lbls.to(device)
                
                # Only use known samples (labels 0 and 4)
                known_mask = (labels == 0) | (labels == 4)
                if not known_mask.any():
                    logger.info("No known samples found in this batch")
                    continue
                    
                data = data[known_mask]
                labels = true_lbls[known_mask]
            
            # Get reconstructions
            reconstructed, _ = model(data, return_latent=True, return_classifier=False)
            
            # Compute reconstruction error
            error = ((data - reconstructed) ** 2).mean(dim=(1, 2))
            
            # Store errors and labels
            reconstruction_errors.extend(error.cpu().numpy())
            true_labels.extend(labels.cpu().numpy())
    
    reconstruction_errors = np.array(reconstruction_errors)
    true_labels = np.array(true_labels)
    
    # Try different thresholds
    min_error = np.min(reconstruction_errors)
    max_error = np.max(reconstruction_errors)
    thresholds = np.linspace(min_error, max_error, 10000)
    
    best_score = -1
    optimal_threshold = None
    
    # Evaluate each threshold
    for threshold in thresholds:
        # Convert reconstruction errors to binary predictions
        predictions = (reconstruction_errors >= threshold).astype(int)
        
        # Calculate metrics
        precision, recall, f1, _ = precision_recall_fscore_support(
            true_labels, predictions, average='binary', pos_label=pos_label
        )
        
        # Update best threshold based on target metric
        current_score = 0
        if target_metric == 'precision':
            current_score = precision
        elif target_metric == 'recall':
            current_score = recall
        else:  # default to f1
            current_score = f1
            
        if current_score > best_score:
            best_score = current_score
            optimal_threshold = threshold
    
    # Log threshold statistics
    logger.info(f"Optimal threshold (maximizing {target_metric}): {optimal_threshold:.6f}")
    logger.info(f"Achieved {target_metric} score: {best_score:.4f}")
    
    # Log distribution of samples around threshold
    above_threshold = sum(e >= optimal_threshold for e in reconstruction_errors)
    below_threshold = sum(e < optimal_threshold for e in reconstruction_errors)
    total_samples = len(reconstruction_errors)
    
    logger.info(f"Threshold Statistics: {below_threshold}/{total_samples} ({100*below_threshold/total_samples:.2f}%) samples below threshold")
    logger.info(f"Threshold Statistics: {above_threshold}/{total_samples} ({100*above_threshold/total_samples:.2f}%) samples above threshold")
    
    return optimal_threshold

def test_model(model, x_te, y_te, true_labels, batch_size, threshold, device='cuda'):
    """
    Evaluates the trained model using:
    - Reconstruction Loss
    - Learned Anomaly Threshold
    - AUC, Precision, Recall, F1-score
    """
    from .data import CustomDataset
    
    logger.info("Testing Model...")
    test_dataset = CustomDataset(x_te, y_te, true_labels)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

    model.eval()
    all_losses = []
    all_true_labels = []
    loss_list = []
    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Testing"):
            data, labels, true_labels = batch
            data = data.to(device)
            
            # Handle different model types
            if isinstance(model, TransformerWithClassifierHead):
                outputs = model(data, return_latent=False, return_classifier=False)
            else:
                outputs = model(data)
            
            # Calculate MSE loss
            error = nn.functional.mse_loss(outputs, data, reduction='none').mean(dim=(1, 2))

            loss_list.extend(error.tolist())
            all_true_labels.extend(true_labels.tolist())
      
    loss_list = np.array(loss_list)
    all_true_labels = np.array(all_true_labels)

    predictions = (loss_list >= threshold).astype(int)

    # Calculate ROC curve and AUC
    # Count occurrences of each class in y_te
    normal_count = np.sum(y_te == 0)
    abnormal_count = np.sum(y_te == 1)
    logger.info(f"Test Dataset Summary - Normal samples: {normal_count}, Abnormal samples: {abnormal_count}")
    fpr, tpr, threshold_auto = roc_curve(all_true_labels, loss_list, pos_label=1)
    auc_score = auc(fpr, tpr)
    
    # Calculate metrics
    precision, recall, f1_score, _ = precision_recall_fscore_support(all_true_labels, predictions, average='binary', pos_label=0)
    
    logger.info(f"Metrics - AUC: {auc_score:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}, F1 Score: {f1_score:.4f}")
    
    return auc_score

def classifier_test_model(model, x_te, y_te, true_labels, batch_size, device='cuda'):
    """
    Evaluates the TransformerWithClassifierHead model using the classifier head.
    Instead of using reconstruction error, this directly uses the binary classifier output.
    
    Args:
        model: Trained TransformerWithClassifierHead model
        x_te: Test data
        y_te: Test labels (for tracking only)
        true_labels: Ground truth labels
        batch_size: Batch size for testing
        device: Device for computation
        
    Returns:
        auc_score: AUC score of the classifier
    """
    from .data import CustomDataset
    from sklearn.metrics import roc_curve, auc, precision_recall_fscore_support, accuracy_score
    
    logger.info("Testing Model using Classifier Head...")
    test_dataset = CustomDataset(x_te, y_te, true_labels)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

    model.eval()
    classifier_scores = []
    all_true_labels = []
    
    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Testing with classifier"):
            data, labels, true_labels = batch
            data = data.to(device)
            
            # Get classifier predictions
            _, latent = model(data, return_latent=True, return_classifier=False)
            latent_pooled = torch.mean(latent, dim=1)  # Mean pooling
            classification = model.classifier(latent_pooled)
            # classification = model.classifier(data)  # Uses the model's classify method
            scores = classification.squeeze().cpu().numpy()
            
            classifier_scores.extend(scores)
            all_true_labels.extend(true_labels.tolist())
    
    classifier_scores = np.array(classifier_scores)
    all_true_labels = np.array(all_true_labels)
    
    # Convert scores to binary predictions using 0.5 as threshold
    predictions = (classifier_scores >= 0.5).astype(int)
    
    # Calculate ROC curve and AUC
    normal_count = np.sum(y_te == 0)
    abnormal_count = np.sum(y_te == 1)
    logger.info(f"Test Dataset Summary - Normal samples: {normal_count}, Abnormal samples: {abnormal_count}")
    
    # ROC curve uses scores, not binary predictions
    fpr, tpr, thresholds = roc_curve(all_true_labels, classifier_scores, pos_label=1)
    auc_score = auc(fpr, tpr)
    
    # Calculate metrics using binary predictions
    accuracy = accuracy_score(all_true_labels, predictions)
    precision, recall, f1_score, _ = precision_recall_fscore_support(
        all_true_labels, predictions, average='binary', pos_label=1
    )
    
    logger.info(f"Classifier Metrics - AUC: {auc_score:.4f}, Acc: {accuracy:.4f}, "
                f"Precision: {precision:.4f}, Recall: {recall:.4f}, F1 Score: {f1_score:.4f}")
    
    # Return the AUC score for consistent API with test_model
    return auc_score

def triplet_classifier_test_model(model, x_te, y_te, true_labels, batch_size, device='cuda'):
    """
    Evaluates the TransformerWithClassifierHead model using the classifier head.
    Instead of using reconstruction error, this directly uses the binary classifier output.
    
    Args:
        model: Trained TransformerWithClassifierHead model
        x_te: Test data
        y_te: Test labels (for tracking only)
        true_labels: Ground truth labels
        batch_size: Batch size for testing
        device: Device for computation
        
    Returns:
        auc_score: AUC score of the classifier
    """
    from .data import CustomDataset
    from sklearn.metrics import roc_curve, auc, precision_recall_fscore_support, accuracy_score
    
    logger.info("Testing Model using Classifier Head...")
    
    # Create standard dataset for testing
    test_dataset = CustomDataset(x_te, y_te, true_labels)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

    model.eval()
    classifier_scores = []
    all_true_labels = []
    
    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Testing with classifier"):
            data, labels, true_labels = batch
            data = data.to(device)
            
            # Get classifier predictions
            _, latent = model(data, return_latent=True, return_classifier=False)
            latent_pooled = torch.mean(latent, dim=1)  # Mean pooling
            classification = model.classifier(latent_pooled)
            scores = classification.squeeze().cpu().numpy()

            classifier_scores.extend(scores)
            all_true_labels.extend(true_labels.tolist())
    
    classifier_scores = np.array(classifier_scores)
    all_true_labels = np.array(all_true_labels)
    
    # Convert scores to binary predictions using 0.5 as threshold
    predictions = (classifier_scores >= 0.5).astype(int)
    
    # Calculate ROC curve and AUC
    normal_count = np.sum(all_true_labels == 0)
    abnormal_count = np.sum(all_true_labels == 1)
    logger.info(f"Test Dataset Summary - Normal samples: {normal_count}, Abnormal samples: {abnormal_count}")
    
    # ROC curve uses scores, not binary predictions
    fpr, tpr, thresholds = roc_curve(all_true_labels, classifier_scores, pos_label=1)
    auc_score = auc(fpr, tpr)
    
    # Calculate metrics using binary predictions
    accuracy = accuracy_score(all_true_labels, predictions)
    precision, recall, f1_score, _ = precision_recall_fscore_support(
        all_true_labels, predictions, average='binary', pos_label=1
    )
    
    logger.info(f"Classifier Metrics - AUC: {auc_score:.4f}, Acc: {accuracy:.4f}, "
                f"Precision: {precision:.4f}, Recall: {recall:.4f}, F1 Score: {f1_score:.4f}")
    
    return auc_score

