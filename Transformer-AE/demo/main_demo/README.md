# Log Anomaly Detection with Transformer Autoencoder

This project implements a Transformer-based Autoencoder model for log anomaly detection with a novel supervised-unsupervised hybrid approach.

## Overview

The system uses a Transformer Autoencoder to detect anomalies in system logs with three types of training data:

1. **Known Normal Logs**: Labeled normal logs
2. **Known Abnormal Logs**: A small percentage of labeled abnormal logs (helps guide the model)
3. **Unknown Logs**: Mixture of unlabeled normal and abnormal logs

During training, the model dynamically categorizes unknown samples into:
- Unknown Normal (similar to known normal)
- Unknown Abnormal (different from known normal)

## Key Features

- **Transformer Architecture**: Leverages attention mechanisms for better context understanding
- **Dynamic Sample Categorization**: Iteratively labels unknown data during training
- **Hybrid Loss Function**: Combines reconstruction loss with inverse reconstruction loss for abnormal samples
- **Adaptive Threshold**: Automatically determines optimal anomaly threshold

## Model Structure

The Transformer Autoencoder consists of:
- Multi-head attention layers
- Positional encoding
- Bottleneck FC layers
- Symmetric decoder

## Usage

### Training

To train a model with both normal and abnormal samples:
