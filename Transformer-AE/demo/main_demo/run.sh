#!/bin/bash

# Create necessary directories
mkdir -p ../saved_model/BGL
mkdir -p ../trace_logs

# Parse command-line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --pure)
      PURE_FLAG="--pure"
      shift
      ;;
    *)
      echo "Unknown option: $1"
      exit 1
      ;;
  esac
done

# Train the model
echo "Starting model training..."
if [ -n "$PURE_FLAG" ]; then
  echo "Using pure reconstruction training mode"
  python main.py --mode train --pure
else
  echo "Using triplet loss training mode with both normal and abnormal data"
  python main.py --mode train
fi

# Test the model
echo "Testing trained model..."
python main.py --mode test

echo "Process completed!"