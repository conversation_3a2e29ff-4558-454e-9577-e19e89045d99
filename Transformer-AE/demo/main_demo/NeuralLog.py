import os
import sys
import argparse
import logging
import datetime
import random

sys.path.append("../")
sys.path.append("../../")

import pickle
import numpy as np
from tensorflow.keras.utils import Sequence
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping
from tensorflow.keras.losses import SparseCategoricalCrossentropy
from official.nlp import optimization
from sklearn.utils import shuffle

from neurallog.models import NeuralLog
from neurallog import main_data_loader
from neurallog.utils import classification_report

from sklearn.metrics import roc_curve, auc, precision_recall_fscore_support
from sklearn.svm import SVC

# Set up logging
log_dir = "../trace_logs"
os.makedirs(log_dir, exist_ok=True)
timestamp = datetime.datetime.now().strftime("%Y%m%d-%H%M%S")

# Default parameters
embed_dim = 768  # Embedding size for each token
max_len = 50

# Configure logging
trace_log_file = f"{log_dir}/{timestamp}_NeuralLog.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler(trace_log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger()

# Set random seeds
np.random.seed(42)
random.seed(42)


class BatchGenerator(Sequence):

    def __init__(self, X, Y, batch_size):
        self.X, self.Y = X, Y
        self.batch_size = batch_size

    def __len__(self):
        return int(np.ceil(len(self.X) / float(self.batch_size)))

    def __getitem__(self, idx):
        # print(self.batch_size)
        dummy = np.zeros(shape=(embed_dim,))
        x = self.X[idx * self.batch_size:min((idx + 1) * self.batch_size, len(self.X))] # len(X) == batch_size; x is a batch has index idx: each record contains window_size messages

        X = np.zeros((len(x), max_len, embed_dim))      # X is 

        Y = np.zeros((len(x), 2))
        item_count = 0     # index for sequence in batch

        # loop each sequence in batch
        # resize sequence and padding to original sequences have window_size to max_len
        for i in range(idx * self.batch_size, min((idx + 1) * self.batch_size, len(self.X))):
            x = self.X[i]
            if len(x) > max_len:
                x = x[-max_len:]
            # padding the first (max_len-len(x)) elements
            x = np.pad(np.array(x), pad_width=((max_len - len(x), 0), (0, 0)), mode='constant',
                       constant_values=0)
            X[item_count] = np.reshape(x, [max_len, embed_dim])
            Y[item_count] = self.Y[i]
            item_count += 1

        return X[:], Y[:, 0]


def train_generator(training_generator, validate_generator, num_train_samples, num_val_samples, batch_size,
                    epoch_num, model_name=None, lr_rate=3e-4):
    epochs = epoch_num
    steps_per_epoch = num_train_samples
    num_train_steps = steps_per_epoch * epochs
    num_warmup_steps = int(0.1 * num_train_steps)

    init_lr = 3e-4
    optimizer = optimization.create_optimizer(init_lr=lr_rate,
                                              num_train_steps=num_train_steps,
                                              num_warmup_steps=num_warmup_steps,
                                              optimizer_type='adamw')

    loss_object = SparseCategoricalCrossentropy()

    model = NeuralLog(768, ff_dim=2048, max_len=max_len, num_heads=12, dropout=0.1)

    # model.load_weights("hdfs_transformer.hdf5")

    model.compile(loss=loss_object,
                  optimizer=optimizer)

    print(model.summary())

    # checkpoint
    filepath = model_name
    checkpoint = ModelCheckpoint(filepath,
                                 monitor='val_loss',
                                 verbose=1,
                                 save_best_only=True,
                                 mode='min',
                                 save_weights_only=True)
    early_stop = EarlyStopping(
        monitor='val_loss', min_delta=0, patience=5, verbose=0, mode='auto',
        baseline=None, restore_best_weights=True
    )
    callbacks_list = [checkpoint, early_stop]

    model.fit_generator(generator=training_generator,
                        steps_per_epoch=int(num_train_samples / batch_size),
                        epochs=epoch_num,
                        verbose=1,
                        validation_data=validate_generator,
                        validation_steps=int(num_val_samples / batch_size),
                        workers=16,
                        max_queue_size=32,
                        callbacks=callbacks_list,
                        shuffle=True
                        )
    return model


def train(X, Y, epoch_num, batch_size, model_file=None, model_type=None, lr_rate=3e-4):
    X, Y = shuffle(X, Y)
    n_samples = len(X)
    train_x, train_y = X[:int(n_samples * 90 / 100)], Y[:int(n_samples * 90 / 100)]
    val_x, val_y = X[int(n_samples * 90 / 100):], Y[int(n_samples * 90 / 100):]

    if model_type == "transformer":
        training_generator, num_train_samples = BatchGenerator(train_x, train_y, batch_size), len(train_x)
        validate_generator, num_val_samples = BatchGenerator(val_x, val_y, batch_size), len(val_x)

        logger.info("Number of training samples: {0} - Number of validating samples: {1}".format(num_train_samples,
                                                                                        num_val_samples))

        model = train_generator(training_generator, validate_generator, num_train_samples, num_val_samples, batch_size,
                                epoch_num, model_name=model_file, lr_rate=lr_rate)

        return model
    
def test_model(model, x, y, batch_size):
    x, y = shuffle(x, y)

    # x, y = x[: len(x) // batch_size * batch_size], y[: len(y) // batch_size * batch_size]
    # print(x)
    test_loader = BatchGenerator(x, y, batch_size)

    prediction = model.predict_generator(test_loader, steps=(len(x) // batch_size + 1), workers=16, max_queue_size=32,
                                         verbose=1)

    predicted_label = np.argmax(prediction, axis=1)
    y = y[:len(predicted_label)]
    report = classification_report(np.array(y), predicted_label)
    logger.info(report)

    FPR_auto, TPR_auto, thresholds_auto = roc_curve(y, prediction[:,1], pos_label=1)
    auc_auto = auc(FPR_auto, TPR_auto)
    logger.info("AUC - Transformer Classification: {}".format(auc_auto))
    
    # Calculate precision, recall, and F1 score
    precision, recall, f1, _ = precision_recall_fscore_support(
        y, predicted_label, average='binary', pos_label=1
    )
    logger.info(f"Precision: {precision:.4f}, Recall: {recall:.4f}, F1: {f1:.4f}")
    return {"auc": auc_auto, "precision": precision, "recall": recall, "f1": f1}


if __name__ == '__main__':
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='NeuralLog for Log Anomaly Detection')
    parser.add_argument('--mode', type=str, choices=['train', 'test'], required=True,
                        help='Mode: train a new model or test an existing model')
    parser.add_argument('--dataset', type=str, default='BGL', choices=['BGL', 'SPIRIT', 'HDFS', 'TDB'],
                        help='Dataset to use (default: BGL)')
    parser.add_argument('--log_name', type=str, default='1m',
                        help='Log file name (default: 1m)')
    parser.add_argument('--train_ratio', type=float, default=0.8,
                        help='Ratio of data to use for training (default: 0.8)')
    parser.add_argument('--window_size', type=int, default=20,
                        help='Window size for sequence processing (default: 20)')
    parser.add_argument('--step_size', type=int, default=20,
                        help='Step size for sliding window (default: 20)')
    parser.add_argument('--batch_size', type=int, default=128,
                        help='Batch size for training (default: 128)')
    parser.add_argument('--epochs', type=int, default=100,
                        help='Number of epochs for training (default: 100)')
    parser.add_argument('--lr', type=float, default=0.05,
                        help='Learning rate (default: 0.05)')
    parser.add_argument('--model_file', type=str, default=None,
                        help='Path to save/load model (default: auto-generated)')
    parser.add_argument('--normal_percent', type=float, default=0.5,
                        help='Percentage of normal data to use as known normal (default: 0.5)')
    parser.add_argument('--abnormal_percent', type=float, default=0.025,
                        help='Percentage of abnormal data to use as known abnormal (default: 0.025)')
    parser.add_argument('--cache', action='store_true',
                        help='Use cached embedding data instead of processing raw logs')
    parser.add_argument('--force', action='store_true',
                        help='Force overwrite of existing model file')
    parser.add_argument('--oov_set_ratio', type=float, default=None,
                        help='Ratio of data to use for OOV testing (default: None)')
    args = parser.parse_args()
    
    # Set log file based on dataset
    log_file = f"../../logs/{args.dataset}/{args.log_name}"
    logger.info(f"Using log file: {log_file}")
    
    # Set model file if not provided
    if args.model_file is None:
        model_dir = f'saved_model/{args.dataset.lower()}'
        os.makedirs(model_dir, exist_ok=True)
        if args.mode == 'train':
            # Use a consistent naming scheme that includes log name and abnormal_percent
            model_name = f"NeuralLog_{args.dataset.lower()}_{args.log_name}_w{args.window_size}_s{args.step_size}_a{args.abnormal_percent}_transformer.hdf5"
            args.model_file = f'{model_dir}/{model_name}'
            
            # Check if model already exists
            if os.path.exists(args.model_file) and not args.force:
                logger.warning(f"Model file already exists: {args.model_file}")
                logger.warning("Use --force to overwrite the existing model")
                logger.warning("Exiting without training")
                sys.exit(1)
                
            # Log that we're using a standardized name
            logger.info(f"Using standardized model name: {model_name}")
        else:  # test mode
            # Find the model file that matches the current parameters, including abnormal_percent
            expected_model_name = f"NeuralLog_{args.dataset.lower()}_{args.log_name}_w{args.window_size}_s{args.step_size}_a{args.abnormal_percent}_transformer.hdf5"
            expected_model_path = os.path.join(model_dir, expected_model_name)
            
            if os.path.exists(expected_model_path):
                args.model_file = expected_model_path
                logger.info(f"Using model: {expected_model_name}")
            else:
                logger.error(f"Model file not found: {expected_model_path}")
                logger.error(f"Please train a model first with these parameters or specify a model file with --model_file")
                sys.exit(1)
    
    logger.info(f"Model {'will be saved to' if args.mode == 'train' else 'loaded from'}: {args.model_file}")
    
    # Set up paths for cached embedding data
    embedding_dir = f"../../logs/{args.dataset}/encoded_data"
    os.makedirs(embedding_dir, exist_ok=True)

    # Define embedding file paths (include oov_set_ratio if provided)
    if args.oov_set_ratio is not None:
        embedding_prefix = f"{args.log_name}_ratio{args.train_ratio}_oov{args.oov_set_ratio}_w{args.window_size}_s{args.step_size}"
    else:
        embedding_prefix = f"{args.log_name}_ratio{args.train_ratio}_w{args.window_size}_s{args.step_size}"
    x_tr_path = os.path.join(embedding_dir, f"{embedding_prefix}_x_tr.pkl")
    y_tr_path = os.path.join(embedding_dir, f"{embedding_prefix}_y_tr.pkl")
    x_te_path = os.path.join(embedding_dir, f"{embedding_prefix}_x_te.pkl")
    y_te_path = os.path.join(embedding_dir, f"{embedding_prefix}_y_te.pkl")

    # Load or process data
    if args.cache and all(os.path.exists(p) for p in [x_tr_path, y_tr_path, x_te_path, y_te_path]):
        # Load cached embedding data
        logger.info("Loading cached embedding data")
        try:
            x_tr_full = pickle.load(open(x_tr_path, "rb"))
            y_tr_full = pickle.load(open(y_tr_path, "rb"))
            x_te = pickle.load(open(x_te_path, "rb"))
            y_te = pickle.load(open(y_te_path, "rb"))
            logger.info(f"Successfully loaded cached embedding data")
        except Exception as e:
            logger.error(f"Error loading cached embedding data: {e}")
            logger.info("Processing raw logs instead")
            args.cache = False  # Fall back to processing raw logs
    
    if not args.cache or not all(os.path.exists(p) for p in [x_tr_path, y_tr_path, x_te_path, y_te_path]):
        # Process raw logs
        logger.info("Processing raw logs to generate embeddings")
        if args.oov_set_ratio is not None:
            (x_tr_full, y_tr_full), (x_te, y_te) = main_data_loader.load_supercomputers(
                log_file, train_ratio=args.train_ratio, oov_set_ratio=args.oov_set_ratio,
                windows_size=args.window_size, step_size=args.step_size, e_type='bert', mode='imbalance')
        else:
            (x_tr_full, y_tr_full), (x_te, y_te) = main_data_loader.load_supercomputers(
                log_file, train_ratio=args.train_ratio, windows_size=args.window_size,
                step_size=args.step_size, e_type='bert', mode='imbalance')
        
        # Save embeddings for future use
        logger.info(f"Saving generated embeddings to {embedding_dir}")
        try:
            pickle.dump(x_tr_full, open(x_tr_path, "wb"))
            pickle.dump(y_tr_full, open(y_tr_path, "wb"))
            pickle.dump(x_te, open(x_te_path, "wb"))
            pickle.dump(y_te, open(y_te_path, "wb"))
            logger.info("Successfully saved embeddings")
        except Exception as e:
            logger.error(f"Error saving embeddings: {e}")
    
    # Convert y_tr_full and y_te to numpy arrays if they aren't already
    y_tr_full = np.array(y_tr_full)
    y_te = np.array(y_te)
    
    # Log original data statistics
    logger.info(f"Original training data - Normal: {sum(y_tr_full == 0)}, Abnormal: {sum(y_tr_full == 1)}")
    logger.info(f"Original testing data - Normal: {sum(y_te == 0)}, Abnormal: {sum(y_te == 1)}")
    
    if args.mode == 'train':
        # Split data into normal and abnormal samples
        normal_tr_indices = np.where(y_tr_full == 0)[0]
        abnormal_tr_indices = np.where(y_tr_full == 1)[0]
        
        # Create lists of normal and abnormal samples
        normal_samples = [x_tr_full[i] for i in normal_tr_indices]
        abnormal_samples = [x_tr_full[i] for i in abnormal_tr_indices]
        
        # Calculate split points based on percentages
        num_known_normal = int(len(normal_samples) * args.normal_percent)
        num_known_abnormal = int(len(abnormal_samples) * args.abnormal_percent)
        # num_known_abnormal = 100
        
        # Select known normal and known abnormal samples
        known_normal_data = normal_samples[:num_known_normal]
        known_abnormal_data = abnormal_samples[:num_known_abnormal]
        
        # Create unknown data (remaining normal + remaining abnormal)
        unknown_data = normal_samples[num_known_normal:] + abnormal_samples[num_known_abnormal:]
        
        # Create true_labels for unknown data
        unknown_true_labels = [0] * (len(normal_samples) - num_known_normal) + [1] * (len(abnormal_samples) - num_known_abnormal)
        
        # Log data split statistics
        logger.info(f"Known Normal Samples: {len(known_normal_data)} ({100*len(known_normal_data)/len(normal_samples):.1f}% of normal)")
        logger.info(f"Known Abnormal Samples: {len(known_abnormal_data)} ({100*len(known_abnormal_data)/len(abnormal_samples):.1f}% of abnormal)")
        logger.info(f"Unlabeled Samples: {len(unknown_data)}")
        logger.info(f"Unknown Normal Samples: {len(normal_samples[num_known_normal:])}")
        logger.info(f"Unknown Abnormal Samples: {len(abnormal_samples) - len(known_abnormal_data)}")
        
        # Combine known normal and known abnormal for training
        x_tr = known_normal_data + known_abnormal_data
        y_tr = [0] * len(known_normal_data) + [1] * len(known_abnormal_data)
        
        # Shuffle training data
        x_tr, y_tr = shuffle(x_tr, y_tr)
        
        # Save prepared data for future use
        save_dir = os.path.join("../saved_data", f"{args.dataset}_{args.log_name}_prepared_data")
        os.makedirs(save_dir, exist_ok=True)
        
        data_dict = {
            "dataset": args.dataset,
            "log_name": args.log_name,
            "window_size": args.window_size,
            "step_size": args.step_size,
            "train_ratio": args.train_ratio,
            "normal_percent": args.normal_percent,
            "abnormal_percent": args.abnormal_percent,
            "known_normal": known_normal_data,
            "known_abnormal": known_abnormal_data,
            "unknown": unknown_data,
            "unknown_true_labels": unknown_true_labels,
            "test_data": x_te,
            "test_labels": y_te
        }
        
        # Use a more descriptive filename that includes configuration
        config_str = f"w{args.window_size}_s{args.step_size}_n{args.normal_percent}_a{args.abnormal_percent}"
        save_path = os.path.join(save_dir, f"neurallog_prepared_data_{config_str}.pkl")
        with open(save_path, "wb") as f:
            pickle.dump(data_dict, f)
        
        logger.info(f"Saved prepared datasets to {save_path}")
        
        # Train model
        logger.info(f"Starting training with {args.epochs} epochs, batch size {args.batch_size}, learning rate {args.lr}")
        model = train(x_tr, y_tr, epoch_num=args.epochs, batch_size=args.batch_size, 
                     model_file=args.model_file, model_type='transformer', lr_rate=args.lr)
        
        # Test model with the trained model
        logger.info("Testing model on test data")
        metrics = test_model(model, x_te, y_te, batch_size=args.batch_size)
        
        # Save metrics to file with detailed naming
        metrics_filename = f"{args.dataset.lower()}_{args.log_name}_w{args.window_size}_s{args.step_size}_a{args.abnormal_percent}_metrics.json"
        metrics_file = os.path.join(model_dir, metrics_filename)
        
        # Add configuration metadata to metrics
        metrics['config'] = {
            'dataset': args.dataset,
            'log_name': args.log_name,
            'window_size': args.window_size,
            'step_size': args.step_size,
            'train_ratio': args.train_ratio,
            'normal_percent': args.normal_percent,
            'abnormal_percent': args.abnormal_percent,
            'batch_size': args.batch_size,
            'epochs': args.epochs,
            'learning_rate': args.lr,
            'model_file': args.model_file
        }
        
        import json
        with open(metrics_file, 'w') as f:
            json.dump(metrics, f, indent=4)
        
        # Log metrics in one line for easy parsing
        logger.info(f"METRICS | Model: {os.path.basename(args.model_file)} | Dataset: {args.dataset} | AUC: {metrics['auc']:.4f} | Precision: {metrics['precision']:.4f} | Recall: {metrics['recall']:.4f} | F1: {metrics['f1']:.4f}")
        logger.info(f"Metrics saved to {metrics_file}")
        
    else:  # Test mode
        # Check if model file exists
        if not os.path.exists(args.model_file):
            logger.error(f"Model file {args.model_file} not found. Please provide a valid model file.")
            sys.exit(1)
            
        # Load the model
        logger.info(f"Loading model from {args.model_file}")
        try:
            # Create a new instance of the NeuralLog model
            logger.info("Initializing NeuralLog model...")
            model = NeuralLog(
                embed_dim=768, 
                ff_dim=2048, 
                max_len=args.window_size, 
                num_heads=12, 
                dropout=0.1
            )
            
            # Compile the model first (needed before loading weights)
            optimizer = optimization.create_optimizer(
                init_lr=args.lr,
                num_train_steps=1000,  # Doesn't matter for inference
                num_warmup_steps=100,  # Doesn't matter for inference
                optimizer_type='adamw'
            )
            loss_object = SparseCategoricalCrossentropy()
            model.compile(loss=loss_object, optimizer=optimizer)
            
            # Build the model by calling it once with dummy data
            logger.info("Building model architecture...")
            dummy_input = np.zeros((1, args.window_size, 768))
            model(dummy_input)
            
            # Now load the weights
            logger.info(f"Loading weights from {args.model_file}...")
            model.load_weights(args.model_file)
            logger.info("Model weights loaded successfully")
            
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            logger.error("Try checking if the model file exists and has the correct format")
            import traceback
            logger.error(traceback.format_exc())
            sys.exit(1)
        
        # Test the loaded model
        logger.info("Testing loaded model on test data")
        metrics = test_model(model, x_te, y_te, batch_size=args.batch_size)
        
        # Print summary of results in one line
        logger.info(f"TEST RESULTS | Model: {os.path.basename(args.model_file)} | Dataset: {args.dataset} ({args.log_name}) | AUC: {metrics['auc']:.4f} | Precision: {metrics['precision']:.4f} | Recall: {metrics['recall']:.4f} | F1: {metrics['f1']:.4f}")
