import os
import sys
sys.path.append("../../")
import pickle
import numpy as np
import torch
import argparse
import logging
import datetime
import random

from utils.models import TransformerAE, TransformerWithClassifierHead
from utils.training import train, main_train_pure, train_with_classifier_head, train_without_categorization
from utils.training_triplet_classifier import train_classifier_head_with_triplet, train_classifier_head_with_triplet_gaining
from utils.evaluation import test_model, compute_threshold, classifier_test_model, triplet_classifier_test_model
from neurallog import torch_data_loader

# Set up logging
log_dir = "../trace_logs"
os.makedirs(log_dir, exist_ok=True)
timestamp = datetime.datetime.now().strftime("%Y%m%d-%H%M%S")

# Set random seeds
# np.random.seed(42)
# torch.manual_seed(42)
# random.seed(42)

# Model Parameters
percent = 99.7

exp_no = 1

def main():
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Transformer Autoencoder for Log Anomaly Detection')
    parser.add_argument('--mode', type=str, choices=['train', 'test', 'latent'], required=True, 
                        help='Mode: train a new model, test an existing model, or extract latent representations')
    parser.add_argument('--pure', action='store_true', 
                        help='Use pure reconstruction training (default: False)')
    parser.add_argument('--classifier', action='store_true',
                        help='Use transformer with classifier head (default: False)')
    parser.add_argument('--triplet', action='store_true',
                        help='Use old triplet loss training (default: False)')
    parser.add_argument('--triplet_classifier', action='store_true',
                        help='Use new triplet loss with classifier head (default: False)')
    parser.add_argument('--train_ratio', type=float, default=0.8, 
                        help='Ratio of data to use for training (default: 0.8)')
    parser.add_argument('--window_size', type=int, default=20, 
                        help='Window size for sliding window during data preparation (default: 20)')
    parser.add_argument('--stride', type=int, default=1, 
                        help='Step size for sliding window during data preparation (default: 1)')
    parser.add_argument('--oov_set_ratio', type=float, default=None,
                        help='Ratio of data to use for OOV testing (default: None)')
    parser.add_argument('--dataset', type=str, default='BGL', choices=['BGL', 'SPIRIT', 'HDFS', 'TDB'],
                        help='Dataset to use (default: BGL)')
    parser.add_argument('--log_name', type=str, default='BGL_full_log',
                        help='Log file name (default: BGL_full_log)')
    parser.add_argument('--used_abnormal_ratio', type=float, default=0.025,
                        help='Ratio of abnormal samples to use as known abnormal (default: 0.025)')
    args = parser.parse_args()

    model_name = 'AE-w-Triplet'

    # Set dataset and log name from arguments
    global dataset, name, log_file
    dataset = args.dataset
    name = args.log_name
    log_file = f"../../logs/{dataset}/{name}"
    
    # Set up training type based on arguments
    if args.pure:
        training_type = "pure"
    elif args.classifier:
        training_type = "classifier"
    elif args.triplet_classifier:
        training_type = "triplet_classifier"
    elif args.triplet:
        training_type = "triplet"
    else:
        training_type = "triplet"  # Default to old triplet

    # Add used_abnormal_ratio to trace log filename
    trace_log_file = f"{log_dir}/{timestamp}_{dataset}_{name}_w{args.window_size}_s{args.stride}_tr{args.train_ratio}_uar{args.used_abnormal_ratio}_{training_type}"
    
    # Add OOV ratio to log name if in test mode with OOV
    if args.mode == 'test' and args.oov_set_ratio is not None:
        trace_log_file += f"_oov{args.oov_set_ratio}"
    trace_log_file += ".log"

    # Configure logging to both file and console
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(trace_log_file),
            logging.StreamHandler()
        ]
    )
    logger = logging.getLogger()
    logger.info(f"Using device: {device}")
    logger.info(f"Using dataset: {dataset}, log name: {name}")
    logger.info(f"Log file path: {log_file}")

    # Update global variables based on command line arguments
    global max_len, stride
    max_len = args.window_size
    stride = args.stride
    
    logger.info(f"Using window size: {max_len}, stride: {stride}")

    # Set model path with indication of training type (without oov_set_ratio)
    if args.pure:
        training_type = "pure"
    elif args.classifier:
        training_type = "classifier"
    elif args.triplet_classifier:
        training_type = "triplet_classifier"
    elif args.triplet:
        training_type = "triplet"
    else:
        training_type = "triplet"  # Default to old triplet
    
    # Model path includes train_ratio but NOT oov_set_ratio - same model for all OOV rates with same train_ratio
    model_path = f"../saved_model/{dataset}/{exp_no}_{model_name}_{args.window_size}_{args.stride}_{dataset}_{name}_tr{args.train_ratio}_uar{args.used_abnormal_ratio}_{training_type}_transformer.pth"
    logger.info(f"Model path: {model_path}")

    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(model_path), exist_ok=True)

    # Include oov_set_ratio in data loading if provided
    if args.oov_set_ratio is not None:
        logger.info(f"Using OOV set ratio: {args.oov_set_ratio}")
        
        # Check if data with this oov_set_ratio already exists
        oov_data_prefix = f"{name}_ratio{args.train_ratio}_oov{args.oov_set_ratio}_windowsize{args.window_size}_s{args.stride}"
        oov_data_path = f"../../logs/{dataset}/encoded_data/{oov_data_prefix}"
        
        if os.path.exists(f"{oov_data_path}_x_tr.pkl") and \
           os.path.exists(f"{oov_data_path}_y_tr.pkl") and \
           os.path.exists(f"{oov_data_path}_x_te.pkl") and \
           os.path.exists(f"{oov_data_path}_y_te.pkl"):
            logger.info(f"Loading existing data with OOV set ratio {args.oov_set_ratio}")
            x_tr = pickle.load(open(f"{oov_data_path}_x_tr.pkl", "rb"))
            y_tr = pickle.load(open(f"{oov_data_path}_y_tr.pkl", "rb"))
            x_te = pickle.load(open(f"{oov_data_path}_x_te.pkl", "rb"))
            y_te = pickle.load(open(f"{oov_data_path}_y_te.pkl", "rb"))
        else:
            logger.info(f"Generating new data with OOV set ratio {args.oov_set_ratio}")
            # Generate new data with the specified OOV set ratio
            (x_tr, y_tr), (x_te, y_te) = torch_data_loader.load_supercomputers(
                log_file, train_ratio=args.train_ratio, oov_set_ratio=args.oov_set_ratio,
                windows_size=args.window_size, step_size=args.stride, e_type='bert', mode='imbalance')
                
            # Save the generated data
            os.makedirs(f"../../logs/{dataset}/encoded_data", exist_ok=True)
            pickle.dump(x_tr, open(f"{oov_data_path}_x_tr.pkl", "wb"))
            pickle.dump(y_tr, open(f"{oov_data_path}_y_tr.pkl", "wb"))
            pickle.dump(x_te, open(f"{oov_data_path}_x_te.pkl", "wb"))
            pickle.dump(y_te, open(f"{oov_data_path}_y_te.pkl", "wb"))
    else:
        # Use the original data loading code
        data_prefix = f"{name}_ratio{args.train_ratio}_windowsize{args.window_size}_s{args.stride}"
        
        # Load Data
        x_tr = pickle.load(open(f"../../logs/{dataset}/encoded_data/{data_prefix}_x_tr.pkl", "rb"))
        y_tr = pickle.load(open(f"../../logs/{dataset}/encoded_data/{data_prefix}_y_tr.pkl", "rb"))
        x_te = pickle.load(open(f"../../logs/{dataset}/encoded_data/{data_prefix}_x_te.pkl", "rb"))
        y_te = pickle.load(open(f"../../logs/{dataset}/encoded_data/{data_prefix}_y_te.pkl", "rb"))

    y_tr = np.array(y_tr)
    y_te = np.array(y_te)

    logger.info(f"Original training data - Normal: {sum(y_tr == 0)}, Abnormal: {sum(y_tr == 1)}")
    logger.info(f"Original testing data - Normal: {sum(y_te == 0)}, Abnormal: {sum(y_te == 1)}")

    # exit(1)
    # Split data into normal and abnormal samples
    normal_tr_indices = np.where(y_tr == 0)[0]
    abnormal_tr_indices = np.where(y_tr == 1)[0]
    
    # Create lists of normal and abnormal samples
    normal_samples = [x_tr[i] for i in normal_tr_indices]
    abnormal_samples = [x_tr[i] for i in abnormal_tr_indices]

    # Calculate split points for 50-50 division
    num_known_normal = len(normal_samples) // 2  # 50% for known normal
    # Use used_abnormal_ratio to determine known abnormal count
    num_known_abnormal = int(len(abnormal_samples) * args.used_abnormal_ratio)
    if num_known_abnormal < 1 and len(abnormal_samples) > 0:
        num_known_abnormal = 1  # Ensure at least one if possible

    if args.pure:
        known_abnormal_data = []  # No known abnormal in pure mode
        unknown_data = abnormal_samples  # All abnormal samples are unknown
    elif args.classifier or args.triplet_classifier:
        # Get initial known abnormal data
        known_abnormal_data = abnormal_samples[:num_known_abnormal]
        
        # # Calculate how many normal samples to introduce as noise (1/5 of known abnormal)
        # noise_count = num_known_abnormal // 2
        
        # if noise_count > 0:
        #     # Randomly select normal samples from the second half (unknown normal)
        #     unknown_normal_samples = normal_samples[num_known_normal:]
        #     if len(unknown_normal_samples) >= noise_count:
        #         # Randomly select noise_count samples from unknown normal
        #         noise_indices = np.random.choice(len(unknown_normal_samples), noise_count, replace=False)
        #         noise_samples = [unknown_normal_samples[i] for i in noise_indices]
                
        #         # Randomly select indices to replace in known_abnormal_data
        #         replace_indices = np.random.choice(len(known_abnormal_data), noise_count, replace=False)
                
        #         # Replace selected known abnormal samples with normal samples (noise)
        #         for i, idx in enumerate(replace_indices):
        #             known_abnormal_data[idx] = noise_samples[i]
                
        #         logger.info(f"Introduced {noise_count} normal samples as noise into known abnormal data")
        
        # Create unknown data (remaining normal + remaining abnormal)
        unknown_data = normal_samples[num_known_normal:] + abnormal_samples[num_known_abnormal:]
        
        # Create true_labels for unknown data
        unknown_true_labels = [0] * (len(normal_samples) - num_known_normal) + [1] * (len(abnormal_samples) - num_known_abnormal)
        
        # Save prepared data to saved_data/prepared_data folder
        prepared_prefix = f"{name}_ratio{args.train_ratio}_oov{args.oov_set_ratio}_windowsize{args.window_size}_s{args.stride}_uar{args.used_abnormal_ratio}"
        save_dir = os.path.join("../saved_data", f"{dataset}", f"{prepared_prefix}_prepared_data") 


        os.makedirs(save_dir, exist_ok=True)
        
        # Save the prepared datasets
        data_dict = {
            "known_normal": normal_samples[:num_known_normal],
            "known_abnormal": known_abnormal_data,
            "unknown": unknown_data,
            "unknown_true_labels": unknown_true_labels,
            "test_data": x_te,
            "test_labels": y_te,
            "used_abnormal_ratio": args.used_abnormal_ratio
        }
        
        save_path = os.path.join(save_dir, "prepared_data.pkl")
        with open(save_path, "wb") as f:
            pickle.dump(data_dict, f)
            
        logger.info(f"Saved prepared datasets to {save_path}")
        
    else:  # Default triplet mode
        known_abnormal_data = abnormal_samples[:num_known_abnormal]
        unknown_data = normal_samples[num_known_normal:] + abnormal_samples[num_known_abnormal:]
        
        # Create true_labels for unknown data
        unknown_true_labels = [0] * (len(normal_samples) - num_known_normal) + [1] * (len(abnormal_samples) - num_known_abnormal)

    known_normal_data = normal_samples[:num_known_normal]
    true_labels = y_tr

    logger.info(f"Known Normal Samples: {len(known_normal_data)} ({100*len(known_normal_data)/len(normal_samples):.1f}% of normal), "
               f"Known Abnormal Samples: {len(known_abnormal_data)} ({100*len(known_abnormal_data)/len(abnormal_samples):.2f}% of abnormal), "
               f"Unlabeled Samples: {len(unknown_data)}")
    logger.info(f"Unknown Normal Samples: {len(normal_samples[num_known_normal:])}, "
               f"Unknown Abnormal Samples: {len(abnormal_samples) - len(known_abnormal_data)}")
    logger.info(f"Used abnormal ratio: {args.used_abnormal_ratio}")

    # Log testing data statistics
    normal_test_samples = sum(y_te == 0)
    abnormal_test_samples = sum(y_te == 1)
    logger.info(f"Testing Data Statistics:")
    logger.info(f"Total test samples: {len(y_te)}")
    logger.info(f"Normal test samples: {normal_test_samples} ({100 * normal_test_samples / len(y_te):.2f}%)")
    logger.info(f"Abnormal test samples: {abnormal_test_samples} ({100 * abnormal_test_samples / len(y_te):.2f}%)")
    
    if args.mode == 'train':
        logger.info("Mode: Training")
        
        if args.pure:
            logger.info("Using pure reconstruction training with known normal data only")
            model, best_threshold = main_train_pure(
                known_normal_data, 
                epoch_num=1000, 
                batch_size=128,
                model_file=model_path,
                lr_rate=0.00001, 
                device=device
            )
        elif args.triplet_classifier:
            logger.info("Using new triplet loss with classifier head")
            model, best_threshold = train_classifier_head_with_triplet(
                known_normal_data,
                known_abnormal_data,
                unknown_data,
                unknown_true_labels,
                epoch_num=1000,
                batch_size=128,
                model_file=model_path,
                lr_rate=0.00001,
                device=device
            )
        elif args.classifier:
            logger.info("Using classifier head approach with 10% known normal data")
            model, best_threshold = train_without_categorization(
                known_normal_data,
                known_abnormal_data,
                unknown_data,
                true_labels,
                epoch_num=1000,
                batch_size=128,
                model_file=model_path,
                lr_rate=0.00001,
                device=device
            )
        else:  # Default to old triplet
            logger.info("Using old triplet loss training with 50% known normal data")
            model, best_threshold = train(
                known_normal_data, 
                known_abnormal_data, 
                unknown_data, 
                true_labels, 
                epoch_num=1000, 
                batch_size=128, 
                model_file=model_path,
                lr_rate=0.0003, 
                device=device
            )
            
        # Save threshold to file
        threshold_path = model_path.replace('.pth', '_threshold.txt')
        with open(threshold_path, 'w') as f:
            f.write(str(best_threshold))
        logger.info(f"Threshold saved to: {threshold_path}")
        
        # Save metadata
        metadata = {
            'dataset': dataset,
            'name': name,
            'training_type': training_type,
            'train_ratio': args.train_ratio,
            'oov_set_ratio': args.oov_set_ratio,
            'stride': args.stride,
            'window_size': args.window_size,
            'threshold': best_threshold,
            'used_abnormal_ratio': args.used_abnormal_ratio,
            'num_known_abnormal': num_known_abnormal,
            'timestamp': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        metadata_path = model_path.replace('.pth', '_metadata.pkl')
        with open(metadata_path, 'wb') as f:
            pickle.dump(metadata, f)
        logger.info(f"Metadata saved to: {metadata_path}")
            
        # Test the trained model
        auc_auto = test_model(model, x_te, y_te, y_te, batch_size=1024, threshold=best_threshold, device=device)
        logger.info(f"Testing complete. Final AUC: {auc_auto:.4f}")
        
        # If using triplet classifier, also test with classifier head
        if args.triplet_classifier:
            logger.info("Testing with classifier head...")
            auc_classifier = triplet_classifier_test_model(model, x_te, y_te, y_te, batch_size=1024, device=device)
            logger.info(f"Classifier testing complete. Final AUC: {auc_classifier:.4f}")
        
    elif args.mode == 'latent':
        logger.info("Mode: Latent Extraction")
        # Check if model exists
        if not os.path.exists(model_path):
            logger.error(f"Model not found at {model_path}. Please train the model first.")
            sys.exit(1)
        # Only support triplet_classifier option for latent extraction
        if not args.triplet_classifier:
            logger.error("Latent extraction is only supported for --triplet_classifier option.")
            sys.exit(1)
        logger.info("Loading TransformerWithClassifierHead model for latent extraction...")
        full_model = TransformerWithClassifierHead(
            embed_dim=768, num_heads=12, hidden_dims=[512, 128], 
            latent_dim=64, num_layers=2, max_seq_length=max_len, 
            classifier_dims=[8]
        ).to(device)
        full_model.load_state_dict(torch.load(model_path, weights_only=True))
        full_model.eval()
        logger.info("Extracting latent representations for training and testing data...")

        def get_latents_triplet_classifier(model, data, batch_size=256):
            model.eval()
            latents = []
            with torch.no_grad():
                for i in range(0, len(data), batch_size):
                    batch = data[i:i+batch_size]
                    batch_tensor = torch.tensor(np.array(batch), dtype=torch.float32).to(device)
                    _, latent = model(batch_tensor, return_latent=True, return_classifier=False)
                    latent_pooled = torch.mean(latent, dim=1)
                    latents.append(latent_pooled.cpu().numpy())
            return np.concatenate(latents, axis=0)

        train_latents = get_latents_triplet_classifier(full_model, x_tr, batch_size=256)
        test_latents = get_latents_triplet_classifier(full_model, x_te, batch_size=256)

        latent_mode = "triplet_classifier"
        oov_str = f"_oov{args.oov_set_ratio}" if args.oov_set_ratio is not None else ""
        latent_dir = (
            f"../saved_data/{dataset}/latents/"
            f"{name}_tr{args.train_ratio}{oov_str}_uar{args.used_abnormal_ratio}_"
            f"{latent_mode}_w{args.window_size}_s{args.stride}"
        )
        os.makedirs(latent_dir, exist_ok=True)
        train_latent_path = os.path.join(latent_dir, "latent_train.pkl")
        test_latent_path = os.path.join(latent_dir, "latent_test.pkl")
        with open(train_latent_path, "wb") as f:
            pickle.dump({'latents': train_latents, 'labels': y_tr}, f)
        with open(test_latent_path, "wb") as f:
            pickle.dump({'latents': test_latents, 'labels': y_te}, f)
        logger.info(f"Saved training latents to {train_latent_path}")
        logger.info(f"Saved testing latents to {test_latent_path}")
    else:  # Test mode
        logger.info("Mode: Testing")
        # Check if model exists
        if not os.path.exists(model_path):
            logger.error(f"Model not found at {model_path}. Please train the model first.")
            sys.exit(1)
            
        # Load the trained model
        if args.classifier or args.triplet_classifier:
            # For classifier or new triplet approach, load the full model for classifier testing
            logger.info("Loading TransformerWithClassifierHead model...")
            full_model = TransformerWithClassifierHead(
                embed_dim=768, num_heads=12, hidden_dims=[512, 128], 
                latent_dim=64, num_layers=2, max_seq_length=20, 
                classifier_dims=[8]  # Match the dimensions used in training
            ).to(device)
            
            full_model.load_state_dict(torch.load(model_path, weights_only=True))
            
            # For reconstruction-based testing, extract just the autoencoder part
            model_for_recon = full_model.get_autoencoder()
            logger.info(f"Model loaded successfully from {model_path}")
        else:
            # For pure or old triplet mode, use a regular TransformerAE
            full_model = TransformerAE(embed_dim=768, num_heads=12, hidden_dims=[512, 128], 
                                num_layers=2, max_seq_length=max_len, latent_dim=64).to(device)
            full_model.load_state_dict(torch.load(model_path))
            model_for_recon = full_model  # Same model for both tests
            logger.info(f"Model loaded from {model_path}")
        
        # Load threshold if available, otherwise compute it
        threshold_path = model_path.replace('.pth', '_threshold.txt')
        if os.path.exists(threshold_path):
            with open(threshold_path, 'r') as f:
                best_threshold = float(f.read().strip())
            logger.info(f"Threshold loaded: {best_threshold}")
        else:
            # Create a loader with only known normal data for threshold computation
            logger.info("Threshold file not found, computing threshold...")
            from utils.data import CustomDataset
            from torch.utils.data import DataLoader
            normal_dataset = CustomDataset(known_normal_data, [0]*len(known_normal_data), [0]*len(known_normal_data))
            normal_loader = DataLoader(normal_dataset, batch_size=128, shuffle=False)
            best_threshold = compute_threshold(normal_loader, model_for_recon, percent, device)
            
        # Test the model using reconstruction-based approach
        auc_auto = test_model(model_for_recon, x_te, y_te, y_te, batch_size=1024, threshold=best_threshold, device=device)
        logger.info(f"Reconstruction-based testing complete. Final AUC: {auc_auto:.4f}")

        # Save results for this specific OOV rate
        results_dir = f"../results/{dataset}/{training_type}"
        os.makedirs(results_dir, exist_ok=True)
        
        results = {
            'dataset': dataset,
            'name': name,
            'training_type': training_type,
            'train_ratio': args.train_ratio,
            'oov_set_ratio': args.oov_set_ratio,
            'stride': args.stride,
            'window_size': args.window_size,
            'threshold': best_threshold,
            'auc': auc_auto,
            'used_abnormal_ratio': args.used_abnormal_ratio,
            'num_known_abnormal': num_known_abnormal,
            'timestamp': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # If using triplet classifier, also test with classifier head
        if args.triplet_classifier:
            logger.info("Testing with classifier head...")
            # Use the full model for classifier testing
            auc_classifier = triplet_classifier_test_model(full_model, x_te, y_te, y_te, batch_size=1024, device=device)
            logger.info(f"Classifier testing complete. Final AUC: {auc_classifier:.4f}")
            results['auc_classifier'] = auc_classifier
            
        # Save results with train_ratio and OOV rate in the filename
        oov_suffix = f"_oov{args.oov_set_ratio}" if args.oov_set_ratio is not None else "_full"
        uar_suffix = f"_uar{args.used_abnormal_ratio}"
        results_path = f"{results_dir}/{exp_no}_{model_name}_{args.window_size}_{args.stride}_{dataset}_{name}_tr{args.train_ratio}{uar_suffix}{oov_suffix}_results.pkl"
        with open(results_path, 'wb') as f:
            pickle.dump(results, f)
        logger.info(f"Results saved to: {results_path}")

if __name__ == '__main__':
    main()
