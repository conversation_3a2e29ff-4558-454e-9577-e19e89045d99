import os
import sys
sys.path.append("../../")
import gc
import pickle
import math
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset, Sampler
from sklearn.utils import shuffle
from sklearn.metrics import roc_curve, auc, precision_recall_fscore_support, roc_auc_score
from neurallog import torch_data_loader
from torchinfo import summary
from tqdm import tqdm
import random
import logging
import datetime
import copy
import argparse

# Set up logging
log_dir = "../trace_logs"
os.makedirs(log_dir, exist_ok=True)
timestamp = datetime.datetime.now().strftime("%Y%m%d-%H%M%S")

np.random.seed(42)
torch.manual_seed(42)

# Model Parameters
percent = 99.7
embed_dim = 768
max_len = 20
stride = 1
exp_no = 1
model_name = 'AE-w-Triplet'
dataset = 'BGL'
name = "1m-2"
log_file = f"../../logs/{dataset}/{name}_used.log"

trace_log_file = f"{log_dir}/{timestamp}_{dataset}_{model_name}.log"

# Configure logging to both file and console
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler(trace_log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger()

# ==========================
# Triplet Loss Function
# ==========================
class TripletLoss(nn.Module):
    def __init__(self, margin=1.0):
        super(TripletLoss, self).__init__()
        self.margin = margin

    def forward(self, unknown_normal, unknown_abnormal):
        """
        Calculate triplet loss based on distance to origin in latent space
        - known_normal: latent representation of known normal samples
        - unknown_normal: latent representation of unknown normal samples
        - unknown_abnormal: latent representation of unknown abnormal samples
        """
        # For unknown normal samples (should be close to origin)
        unknown_normal_distance = torch.mean(torch.sum(unknown_normal ** 2, dim=-1))
        
        # For abnormal samples (should be far from origin)
        unknown_abnormal_distance = torch.mean(torch.sum(unknown_abnormal ** 2, dim=-1))
        
        # Combined loss:
        # 1. Minimize distance of known normal to origin
        # 2. Apply triplet constraint between unknown normal and unknown abnormal
        triplet_loss = torch.clamp(unknown_normal_distance - unknown_abnormal_distance + self.margin, min=0.0)
        
        return triplet_loss

# ==========================
# Dynamic Threshold Calculation
# ==========================
def compute_threshold(train_loader, model, percent, device):
    """
    Dynamically computes the anomaly threshold using the 3-sigma rule.
    """
    logger.info("Computing Anomaly Threshold...")
    model.eval()
    reconstruction_errors = []
    with torch.no_grad():
        for batch in train_loader:
            data, labels, _ = batch
            known_normal_data = data[labels == 0].to(device)
            reconstructed = model(known_normal_data)
            error = torch.mean((known_normal_data - reconstructed) ** 2, dim=2).mean(dim=1)
            reconstruction_errors.extend(error.cpu().numpy())

    # Calculate threshold as the 95th percentile of reconstruction errors
    threshold = np.percentile(reconstruction_errors, percent)

    logger.info(f"Using {percent} percentile threshold: {threshold:.6f}")
    # Log how many samples are above/below threshold
    above_threshold = sum(e >= threshold for e in reconstruction_errors)
    below_threshold = sum(e < threshold for e in reconstruction_errors)
    total_samples = len(reconstruction_errors)
    
    logger.info(f"Threshold Statistics: {below_threshold}/{total_samples} ({100*below_threshold/total_samples:.2f}%) samples below threshold")
    logger.info(f"Threshold Statistics: {above_threshold}/{total_samples} ({100*above_threshold/total_samples:.2f}%) samples above threshold")
    return threshold

# ==========================
# Batch Generator with Known Normal and Unknown Data
# ==========================
class CustomDataset(Dataset):
    def __init__(self, data, labels, true_labels):
        self.data = np.array(data, dtype=np.float32)
        self.labels = np.array(labels, dtype=np.int64)
        self.true_labels = np.array(true_labels, dtype=np.int64)
        self.data, self.labels, self.true_labels = shuffle(self.data, self.labels, self.true_labels)

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        sample = torch.tensor(self.data[idx])
        label = torch.tensor(self.labels[idx])
        true_label = torch.tensor(self.true_labels[idx])
        return sample, label, true_label

    def get_balanced_batch(self, batch_size):
        normal_indices = np.where(self.labels == 0)[0]
        unknown_indices = np.where(self.labels == 1)[0]
        normal_samples = np.random.choice(normal_indices, batch_size // 2, replace=False)
        unknown_samples = np.random.choice(unknown_indices, batch_size // 2, replace=False)
        batch_indices = np.concatenate((normal_samples, unknown_samples))
        np.random.shuffle(batch_indices)
        return [self[i] for i in batch_indices]

# ==========================
# Custom Balanced Batch Sampler
# ==========================
class BalancedBatchSampler(Sampler):
    """
    Ensures every batch has a balanced mixture of known normal and unknown data
    """
    def __init__(self, dataset, batch_size, normal_ratio=0.5):
        self.dataset = dataset
        self.batch_size = batch_size
        self.normal_ratio = normal_ratio
        
        # Get indices of normal and unknown samples
        self.normal_indices = np.where(self.dataset.labels == 0)[0]
        self.unknown_indices = np.where(self.dataset.labels == 1)[0]
        
        # Calculate number of samples per batch
        self.normal_per_batch = int(self.batch_size * self.normal_ratio)
        self.unknown_per_batch = self.batch_size - self.normal_per_batch
        
        # Calculate number of complete batches
        self.num_batches = min(
            len(self.normal_indices) // self.normal_per_batch,
            len(self.unknown_indices) // self.unknown_per_batch
        )
        
    def __iter__(self):
        # Shuffle indices for each epoch
        np.random.shuffle(self.normal_indices)
        np.random.shuffle(self.unknown_indices)
        
        for batch_idx in range(self.num_batches):
            batch = []
            
            # Get normal samples for this batch
            normal_idx_start = batch_idx * self.normal_per_batch
            normal_idx_end = normal_idx_start + self.normal_per_batch
            batch.extend(self.normal_indices[normal_idx_start:normal_idx_end])
            
            # Get unknown samples for this batch
            unknown_idx_start = batch_idx * self.unknown_per_batch
            unknown_idx_end = unknown_idx_start + self.unknown_per_batch
            batch.extend(self.unknown_indices[unknown_idx_start:unknown_idx_end])
            
            # Shuffle the batch
            random.shuffle(batch)
            
            # Yield the entire batch of indices at once
            yield batch
    
    def __len__(self):
        return self.num_batches

# ==========================
# Transformer Autoencoder Model
# ==========================
class TransformerAE(nn.Module):
    def __init__(self, embed_dim=768, num_heads=12, hidden_dims=[256, 128], latent_dim=64, num_layers=1, max_seq_length=512):
        super(TransformerAE, self).__init__()
        self.embed_dim = embed_dim
        self.max_seq_length = max_seq_length
        
        # Set seed for reproducibility
        torch.manual_seed(42)

        # Input layer (in PyTorch, this is handled implicitly)
        
        # Positional encoding
        self.positional_encoding = self.create_positional_encoding(max_seq_length, embed_dim)

        # Transformer Encoder layers
        self.encoder_layers = nn.ModuleList([
            nn.ModuleList([
                nn.MultiheadAttention(embed_dim=embed_dim, num_heads=num_heads, batch_first=True),
                nn.LayerNorm(embed_dim, eps=1e-6),
                nn.Linear(embed_dim, embed_dim),
                nn.ReLU(),
                nn.LayerNorm(embed_dim, eps=1e-6)
            ]) for _ in range(num_layers)
        ])
        
        # Latent space layers
        self.fc_encoder_hidden_layers = nn.ModuleList([])
        current_dim = self.embed_dim
        for hidden_dim in hidden_dims:
            self.fc_encoder_hidden_layers.append(nn.Linear(current_dim, hidden_dim))
            self.fc_encoder_hidden_layers.append(nn.ReLU())
            current_dim = hidden_dim

        # Final latent layer
        self.fc_latent = nn.Linear(hidden_dims[-1], latent_dim)

        # Decoder layers
        self.fc_decoder_hidden_layers = nn.ModuleList([])
        current_dim = latent_dim
        for hidden_dim in reversed(hidden_dims):
            self.fc_decoder_hidden_layers.append(nn.Linear(current_dim, hidden_dim))
            self.fc_decoder_hidden_layers.append(nn.ReLU())
            current_dim = hidden_dim

        # Output layer
        self.fc_out = nn.Linear(hidden_dims[0], embed_dim)
        
        # Initialize all weights using Xavier uniform (equivalent to GlorotUniform in TF)
        self._init_weights()

    def _init_weights(self):
        """Initialize all layers using Xavier uniform initializer (equivalent to GlorotUniform)"""
        for name, module in self.named_modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.MultiheadAttention):
                # Initialize the projection weights in MultiheadAttention
                nn.init.xavier_uniform_(module.in_proj_weight)
                nn.init.xavier_uniform_(module.out_proj.weight)
                if module.in_proj_bias is not None:
                    nn.init.zeros_(module.in_proj_bias)
                if module.out_proj.bias is not None:
                    nn.init.zeros_(module.out_proj.bias)

    def create_positional_encoding(self, max_len, d_model):
        """Create positional encoding similar to the TF implementation"""
        position = np.arange(max_len)[:, np.newaxis]
        div_term = np.exp(np.arange(0, d_model, 2) * -(np.log(10000.0) / d_model))
        pos_encoding = np.zeros((max_len, d_model))
        pos_encoding[:, 0::2] = np.sin(position * div_term)
        pos_encoding[:, 1::2] = np.cos(position * div_term)
        pos_encoding = pos_encoding[np.newaxis, ...]
        return torch.FloatTensor(pos_encoding).squeeze(0)

    def forward(self, x, return_latent=False):
        # Add positional encoding
        x = x + self.positional_encoding[:x.size(1), :].to(x.device)
        
        # Encoder part
        for attention_layer, norm1, dense_ffn, relu, norm2 in self.encoder_layers:
            # Self-attention
            attn_output, _ = attention_layer(x, x, x)
            x = norm1(x + attn_output)  # Add & Norm
            
            # Feed-forward network
            ffn_output = dense_ffn(x)
            ffn_output = relu(ffn_output)
            x = norm2(x + ffn_output)  # Add & Norm
        
        # Store encoder output for potential return
        encoder_output = x.clone()
        
        # Latent space
        for layer in self.fc_encoder_hidden_layers:
            x = layer(x)
        
        # Get latent representation
        latent = self.fc_latent(x)
        
        # Start decoder path
        x = latent
        
        # Decoder part
        for layer in self.fc_decoder_hidden_layers:
            x = layer(x)
            
        # Output layer
        output = self.fc_out(x)
        
        if return_latent:
            return output, latent
        return output

# ==========================
# Training Function
# ==========================
def train_generator(train_loader, validate_loader, batch_size, epoch_num, model_file=None, lr_rate=3e-4, device='cuda'):

    model = TransformerAE(embed_dim=768, num_heads=12, hidden_dims=[512, 128], \
                          num_layers=2, max_seq_length=max_len, latent_dim=64).to(device)
    
    optimizer = optim.AdamW(model.parameters(), lr=lr_rate)
    
    # Add learning rate scheduler - ReduceLROnPlateau
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, 
        mode='min', 
        factor=0.9,    # Multiply lr by this factor when reducing
        patience=3,    # Number of epochs with no improvement before reducing lr
        verbose=True,  # Print message when lr is reduced
        min_lr=1e-6    # Don't reduce lr below this value
    )
    
    criterion = nn.MSELoss()

    best_val_loss = float('inf')
    best_model = None
    patience = 10
    patience_counter = 0
    
    # Loss weights
    w_known = 1.0       # Weight for known normal reconstruction
    w_unknown_normal = 0.0  # Weight for unknown normal reconstruction (minimize)
    w_unknown_abnormal = 100.0  # Weight for unknown abnormal reconstruction (maximize)
    margin = 3.0        # Margin for unknown abnormal reconstruction loss
    
    # Extract all data from Train DataLoader for dynamic sample categorization
    all_train_data = []
    all_train_labels = []
    all_train_true_labels = []
    
    for batch in train_loader:
        batch_data, batch_labels, batch_true_labels = batch
        all_train_data.append(batch_data.numpy())
        all_train_labels.append(batch_labels.numpy())
        all_train_true_labels.append(batch_true_labels.numpy())
    
    all_train_data = np.concatenate(all_train_data, axis=0)
    all_train_labels = np.concatenate(all_train_labels, axis=0)
    all_train_true_labels = np.concatenate(all_train_true_labels, axis=0)
    
    # Create separate sets for each category in training
    known_normal_indices = np.where(all_train_labels == 0)[0]
    unknown_indices = np.where(all_train_labels == 1)[0].tolist()  # Start with all unknown samples
    unknown_normal_indices = []  # Will be gradually populated
    unknown_abnormal_indices = []  # Will be gradually populated
    
    # Extract all data from Validation DataLoader for dynamic sample categorization
    all_val_data = []
    all_val_labels = []
    all_val_true_labels = []
    
    for batch in validate_loader:
        batch_data, batch_labels, batch_true_labels = batch
        all_val_data.append(batch_data.numpy())
        all_val_labels.append(batch_labels.numpy())
        all_val_true_labels.append(batch_true_labels.numpy())
    
    all_val_data = np.concatenate(all_val_data, axis=0)
    all_val_labels = np.concatenate(all_val_labels, axis=0)
    all_val_true_labels = np.concatenate(all_val_true_labels, axis=0)
    
    # Create separate sets for each category in validation
    val_known_normal_indices = np.where(all_val_labels == 0)[0]
    val_unknown_indices = np.where(all_val_labels == 1)[0].tolist()  # Start with all unknown samples
    val_unknown_normal_indices = []  # Will be gradually populated
    val_unknown_abnormal_indices = []  # Will be gradually populated
    
    logger.info(f"Initial split - Training: Known normal: {len(known_normal_indices)}, Unknown: {len(unknown_indices)}")
    logger.info(f"Initial split - Validation: Known normal: {len(val_known_normal_indices)}, Unknown: {len(val_unknown_indices)}")

    # Training Loop 
    for epoch in range(epoch_num):
        model.train()
        
        # Dynamic sample categorization every 5 epochs
        if epoch > 0 and epoch % 20 == 0:
            logger.info(f"Epoch {epoch+1}: Categorizing unknown samples...")
            
            # Calculate threshold based on current model state
            logger.info(f"Epoch {epoch+1}: Computing anomaly threshold for dynamic categorization...")
            # current_threshold = compute_threshold(train_loader, model, percent, device)
            current_threshold = compute_threshold(train_loader, best_model, percent, device)

            # Process training samples if there are unknown samples left
            if len(unknown_indices) > 0:
                # Create a temporary dataset and loader for remaining unknown samples
                unknown_data_subset = all_train_data[unknown_indices]
                unknown_labels_subset = all_train_labels[unknown_indices]  # All 1's
                unknown_true_labels_subset = all_train_true_labels[unknown_indices]  # Actual labels
                
                temp_dataset = CustomDataset(unknown_data_subset, unknown_labels_subset, unknown_true_labels_subset)
                temp_loader = DataLoader(temp_dataset, batch_size=batch_size, shuffle=True)
                
                # Compute reconstruction errors for all remaining unknown samples
                model.eval()
                reconstruction_errors = []
                with torch.no_grad():
                    for batch in temp_loader:
                        data, _, _ = batch
                        data = data.to(device)
                        reconstructed = best_model(data)
                        error = ((data - reconstructed) ** 2).mean(dim=(1, 2)).cpu().detach().numpy()

                        reconstruction_errors.append(error)
                        # error = criterion(data, reconstructed).cpu().numpy()
                        # reconstruction_errors.extend(error)
                        
                
                # Create a mapping of unknown indices to their reconstruction errors
                reconstruction_errors = np.concatenate(reconstruction_errors)

                index_to_error = {idx: error for idx, error in zip(unknown_indices, reconstruction_errors)}

                # Separate samples based on threshold into normal and abnormal partitions
                normal_partition = [idx for idx in unknown_indices if index_to_error[idx] < current_threshold]
                abnormal_partition = [idx for idx in unknown_indices if index_to_error[idx] >= current_threshold]
                
                logger.info(f"Threshold-based partitioning - Normal: {len(normal_partition)}, Abnormal: {len(abnormal_partition)}")
                
                # Process normal partition if not empty
                new_normal_indices = []
                if normal_partition:
                    # Sort normal partition by reconstruction error
                    normal_partition.sort(key=lambda idx: index_to_error[idx])
                    
                    # Calculate 1% for selection and noise removal
                    noise_removal = max(int(0.01 * len(normal_partition)), 1)
                    normal_select_count = max(int(0.005 * len(normal_partition)), 1)
                    
                    # Remove 1% highest errors as noise
                    normal_partition = normal_partition[:-noise_removal]
                    
                    # Select 1% with lowest errors as new normal samples
                    new_normal_indices = normal_partition[:normal_select_count]
                    unknown_normal_indices.extend(new_normal_indices)
                
                # Process abnormal partition if not empty
                new_abnormal_indices = []
                if abnormal_partition:
                    # Sort abnormal partition by reconstruction error
                    abnormal_partition.sort(key=lambda idx: index_to_error[idx])
                    
                    # Calculate 1% for selection and noise removal
                    noise_removal = max(int(0.01 * len(abnormal_partition)), 1)
                    abnormal_select_count = max(int(0.005 * len(abnormal_partition)), 1)
                    
                    # Remove 1% lowest errors as noise
                    abnormal_partition = abnormal_partition[noise_removal:]
                    
                    # Select 1% with highest errors as new abnormal samples
                    new_abnormal_indices = abnormal_partition[-abnormal_select_count:]
                    unknown_abnormal_indices.extend(new_abnormal_indices)
                
                # Update unknown indices by removing selected and noise samples
                selected_indices = set(new_normal_indices + new_abnormal_indices)
                unknown_indices = [idx for idx in unknown_indices if idx not in selected_indices]
                
                logger.info(f"Training categorized - New normal: {len(new_normal_indices)}, "
                          f"New abnormal: {len(new_abnormal_indices)}, "
                          f"Remaining unknown: {len(unknown_indices)}")
                logger.info(f"Training total categorized - Normal: {len(unknown_normal_indices)}, "
                          f"Abnormal: {len(unknown_abnormal_indices)}, "
                          f"Remaining unknown: {len(unknown_indices)}")
            
            # Process validation samples if there are unknown samples left
            if len(val_unknown_indices) > 0:
                # Create a temporary dataset and loader for remaining unknown validation samples
                val_unknown_data_subset = all_val_data[val_unknown_indices]
                val_unknown_labels_subset = all_val_labels[val_unknown_indices]  # All 1's
                val_unknown_true_labels_subset = all_val_true_labels[val_unknown_indices]  # Actual labels
                
                val_temp_dataset = CustomDataset(val_unknown_data_subset, val_unknown_labels_subset, val_unknown_true_labels_subset)
                val_temp_loader = DataLoader(val_temp_dataset, batch_size=batch_size, shuffle=False)
                
                # Compute reconstruction errors for all remaining unknown validation samples
                model.eval()
                val_reconstruction_errors = []
                with torch.no_grad():
                    for batch in val_temp_loader:
                        data, _, _ = batch
                        data = data.to(device)
                        reconstructed = best_model(data)
                        error = criterion(data, reconstructed).cpu().numpy()
                        error = ((data - reconstructed) ** 2).mean(dim=(1, 2)).cpu().detach().numpy()
                        val_reconstruction_errors.append(error)
                
                # Create a mapping of unknown validation indices to their reconstruction errors
                val_reconstruction_errors = np.concatenate(val_reconstruction_errors)

                val_index_to_error = {idx: error for idx, error in zip(val_unknown_indices, val_reconstruction_errors)}

                # Separate samples based on threshold into normal and abnormal partitions
                val_normal_partition = [idx for idx in val_unknown_indices if val_index_to_error[idx] < current_threshold]
                val_abnormal_partition = [idx for idx in val_unknown_indices if val_index_to_error[idx] >= current_threshold]

                logger.info(f"Validation threshold-based partitioning - Normal: {len(val_normal_partition)}, Abnormal: {len(val_abnormal_partition)}")

                # Process normal partition if not empty
                val_new_normal_indices = []
                if val_normal_partition:
                    # Sort normal partition by reconstruction error
                    val_normal_partition.sort(key=lambda idx: val_index_to_error[idx])
                    
                    # Calculate 1% for selection and noise removal
                    noise_removal = max(int(0.01 * len(val_normal_partition)), 1)
                    val_normal_select_count = max(int(0.005 * len(val_normal_partition)), 1)
                    
                    # Remove 1% highest errors as noise
                    val_normal_partition = val_normal_partition[:-noise_removal]
                    
                    # Select 1% with lowest errors as new normal samples
                    val_new_normal_indices = val_normal_partition[:val_normal_select_count]
                    val_unknown_normal_indices.extend(val_new_normal_indices)

                    # val_normal_select_count = max(int(0.005 * len(val_normal_partition)), 1)
                    
                    # # Remove 1% highest errors as noise
                    # val_normal_partition = val_normal_partition[:-val_normal_select_count]
                    
                    # # Select 1% with lowest errors as new normal samples
                    # val_new_normal_indices = val_normal_partition[:val_normal_select_count]
                    # val_unknown_normal_indices.extend(val_new_normal_indices)

                # Process abnormal partition if not empty
                val_new_abnormal_indices = []
                if val_abnormal_partition:
                    # Sort abnormal partition by reconstruction error
                    val_abnormal_partition.sort(key=lambda idx: val_index_to_error[idx])
                    
                    # Calculate 1% for selection and noise removal
                    noise_removal = max(int(0.01 * len(val_abnormal_partition)), 1)
                    val_abnormal_select_count = max(int(0.005 * len(val_abnormal_partition)), 1)
                    
                    # Remove 1% lowest errors as noise
                    val_abnormal_partition = val_abnormal_partition[noise_removal:]
                    
                    # Select 1% with highest errors as new abnormal samples
                    val_new_abnormal_indices = val_abnormal_partition[-val_abnormal_select_count:]
                    val_unknown_abnormal_indices.extend(val_new_abnormal_indices)

                    # val_abnormal_select_count = max(int(0.005 * len(val_abnormal_partition)), 1)
                    
                    # # Remove 1% lowest errors as noise
                    # val_abnormal_partition = val_abnormal_partition[val_abnormal_select_count:]
                    
                    # # Select 1% with highest errors as new abnormal samples
                    # val_new_abnormal_indices = val_abnormal_partition[-val_abnormal_select_count:]
                    # val_unknown_abnormal_indices.extend(val_new_abnormal_indices)

                # Update unknown indices by removing selected and noise samples
                val_selected_indices = set(val_new_normal_indices + val_new_abnormal_indices)
                val_unknown_indices = [idx for idx in val_unknown_indices if idx not in val_selected_indices]

                logger.info(f"Validation categorized - New normal: {len(val_new_normal_indices)}, "
                          f"New abnormal: {len(val_new_abnormal_indices)}, "
                          f"Remaining unknown: {len(val_unknown_indices)}")
                logger.info(f"Validation total categorized - Normal: {len(val_unknown_normal_indices)}, "
                          f"Abnormal: {len(val_unknown_abnormal_indices)}, "
                          f"Remaining unknown: {len(val_unknown_indices)}")
            
            # Create new training dataset with categorized samples
            new_train_labels = all_train_labels.copy()
            
            # Mark categorized samples in the labels array (2=unknown normal, 3=unknown abnormal)
            for idx in unknown_normal_indices:
                new_train_labels[idx] = 2  # Custom label for unknown normal
            for idx in unknown_abnormal_indices:
                new_train_labels[idx] = 3  # Custom label for unknown abnormal
                
            # Create new training dataset and loader with updated labels
            new_train_dataset = CustomDataset(all_train_data, new_train_labels, all_train_true_labels)
            train_loader = DataLoader(new_train_dataset, batch_size=batch_size, shuffle=True)
            
            # Create new validation dataset with categorized samples
            new_val_labels = all_val_labels.copy()
            
            # Mark categorized samples in the validation labels array
            for idx in val_unknown_normal_indices:
                new_val_labels[idx] = 2  # Custom label for unknown normal
            for idx in val_unknown_abnormal_indices:
                new_val_labels[idx] = 3  # Custom label for unknown abnormal
            
            # Create new validation dataset and loader with updated labels
            new_val_dataset = CustomDataset(all_val_data, new_val_labels, all_val_true_labels)
            validate_loader = DataLoader(new_val_dataset, batch_size=batch_size, shuffle=True)
            
            best_val_loss = float("inf")
            patience_counter = 0  # Reset patience counter after categorization

            # Switch back to training mode
            model.train()
        # Reset best_val_loss to zero after the first 5 epochs
        # if epoch == 15:
        #     logger.info(f"Epoch {epoch+1}: Resetting best_val_loss to inf")
        #     best_val_loss = float("inf")
        #     patience_counter = 0  # Also reset patience counter
        
        # Log current learning rate
        current_lr = optimizer.param_groups[0]['lr']
        logger.info(f"Epoch [{epoch+1}/{epoch_num}] - Current learning rate: {current_lr:.6f}")

        total_loss = 0
        total_known_loss = 0
        total_unknown_normal_loss = 0
        total_unknown_abnormal_loss = 0
        batch_count = 0
        
        # Counters for samples in each category
        total_known_samples = 0
        total_unknown_normal_samples = 0
        total_unknown_abnormal_samples = 0
        total_uncategorized_samples = 0

        for batch in tqdm(train_loader, desc=f"Training Epoch {epoch+1}/{epoch_num}"):
            data, labels, _ = batch
            data = data.to(device)
            labels = labels.to(device)

            # Process different categories based on our custom labels
            known_normal_mask = labels == 0
            unknown_mask = labels == 1  # Uncategorized unknown samples
            unknown_normal_mask = labels == 2  # Pre-categorized unknown normal
            unknown_abnormal_mask = labels == 3  # Pre-categorized unknown abnormal
            
            known_normal_batch = data[known_normal_mask]
            unknown_batch = data[unknown_mask]  # Will not be used for triplet loss
            unknown_normal_batch = data[unknown_normal_mask]
            unknown_abnormal_batch = data[unknown_abnormal_mask]

            # Count samples in each category
            total_known_samples += known_normal_mask.sum().item()
            total_unknown_normal_samples += unknown_normal_mask.sum().item()
            total_unknown_abnormal_samples += unknown_abnormal_mask.sum().item()
            total_uncategorized_samples += unknown_mask.sum().item()

            optimizer.zero_grad()
            
            # Initialize losses
            known_normal_loss = torch.tensor(0.0).to(device)
            unknown_normal_loss = torch.tensor(0.0).to(device)
            unknown_abnormal_loss = torch.tensor(0.0).to(device)
            
            # Process known normal data
            if len(known_normal_batch) > 0:
                known_normal_output = model(known_normal_batch)
                known_normal_loss = criterion(known_normal_batch, known_normal_output)

            # Process pre-categorized unknown normal samples
            if len(unknown_normal_batch) > 0:
                unknown_normal_output = model(unknown_normal_batch)
                unknown_normal_loss = criterion(unknown_normal_batch, unknown_normal_output)
            
            # Process pre-categorized unknown abnormal samples
            if len(unknown_abnormal_batch) > 0:
                unknown_abnormal_output = model(unknown_abnormal_batch)
                # abnormal_recon_error = torch.mean((unknown_abnormal_batch - unknown_abnormal_output) ** 2)
                abnormal_recon_error = criterion(unknown_abnormal_batch, unknown_abnormal_output)
                epsilon = 1e-8
                unknown_abnormal_loss = 1.0 / (abnormal_recon_error + epsilon)

            # Combined loss - if no unknown samples have been categorized yet, just use known normal loss
            if total_unknown_normal_samples == 0 and total_unknown_abnormal_samples == 0:
                loss = known_normal_loss
            else:
                loss = (w_known * known_normal_loss + 
                       w_unknown_normal * unknown_normal_loss + 
                       w_unknown_abnormal * unknown_abnormal_loss)
            
            loss.backward()
            optimizer.step()

            total_loss += loss.item()
            total_known_loss += known_normal_loss.item()
            total_unknown_normal_loss += unknown_normal_loss.item()
            total_unknown_abnormal_loss += unknown_abnormal_loss.item()
            batch_count += 1

        # Calculate average losses
        avg_loss = total_loss / batch_count if batch_count > 0 else 0.0
        avg_known_loss = total_known_loss / batch_count if batch_count > 0 else 0.0
        avg_unknown_normal_loss = total_unknown_normal_loss / batch_count if batch_count > 0 else 0.0
        avg_unknown_abnormal_loss = total_unknown_abnormal_loss / batch_count if batch_count > 0 else 0.0
        
        # Log total counts of samples in each category for this epoch
        logger.info(f"Epoch [{epoch+1}/{epoch_num}] Summary - "
                   f"Known normal samples: {total_known_samples}, "
                   f"Unknown normal samples: {total_unknown_normal_samples}, "
                   f"Unknown abnormal samples: {total_unknown_abnormal_samples}, "
                   f"Uncategorized samples: {total_uncategorized_samples}")

        # Validation
        model.eval()
        val_loss = 0
        val_known_loss = 0
        val_unknown_normal_loss = 0
        val_unknown_abnormal_loss = 0
        val_batch_count = 0
        
        # Counters for validation samples in each category
        val_total_known_samples = 0
        val_total_unknown_normal_samples = 0
        val_total_unknown_abnormal_samples = 0
        val_total_uncategorized_samples = 0
        
        with torch.no_grad():
            for batch in tqdm(validate_loader, desc="Validating"):
                data, labels, _ = batch
                data = data.to(device)
                labels = labels.to(device)

                # Process validation data using same categorization as training
                known_normal_mask = labels == 0
                unknown_mask = labels == 1  # Uncategorized unknown samples
                unknown_normal_mask = labels == 2  # Pre-categorized unknown normal
                unknown_abnormal_mask = labels == 3  # Pre-categorized unknown abnormal
                
                known_normal_batch = data[known_normal_mask]
                unknown_batch = data[unknown_mask]
                unknown_normal_batch = data[unknown_normal_mask]
                unknown_abnormal_batch = data[unknown_abnormal_mask]
                
                # Count validation samples in each category
                val_total_known_samples += known_normal_mask.sum().item()
                val_total_unknown_normal_samples += unknown_normal_mask.sum().item()
                val_total_unknown_abnormal_samples += unknown_abnormal_mask.sum().item()
                val_total_uncategorized_samples += unknown_mask.sum().item()

                # Initialize losses
                known_normal_loss = torch.tensor(0.0).to(device)
                unknown_normal_loss = torch.tensor(0.0).to(device)
                unknown_abnormal_loss = torch.tensor(0.0).to(device)
                
                # Process known normal data
                if len(known_normal_batch) > 0:
                    known_normal_output = model(known_normal_batch)
                    known_normal_loss = criterion(known_normal_batch, known_normal_output)
                
                # Process unknown normal data
                if len(unknown_normal_batch) > 0:
                    unknown_normal_output = model(unknown_normal_batch)
                    unknown_normal_loss = criterion(unknown_normal_batch, unknown_normal_output)
                
                # Process unknown abnormal data
                if len(unknown_abnormal_batch) > 0:
                    unknown_abnormal_output = model(unknown_abnormal_batch)
                    # abnormal_recon_error = torch.mean((unknown_abnormal_batch - unknown_abnormal_output) ** 2)
                    abnormal_recon_error = criterion(unknown_abnormal_batch, unknown_abnormal_output)
                    epsilon = 1e-8
                    unknown_abnormal_loss = 1.0 / (abnormal_recon_error+epsilon)
                
                # Combined loss for validation - same approach as training
                if val_total_unknown_normal_samples == 0 and val_total_unknown_abnormal_samples == 0:
                    val_batch_loss = known_normal_loss
                else:
                    val_batch_loss = (w_known * known_normal_loss + 
                                     w_unknown_normal * unknown_normal_loss + 
                                     w_unknown_abnormal * unknown_abnormal_loss)
                
                val_loss += val_batch_loss.item()
                val_known_loss += known_normal_loss.item()
                val_unknown_normal_loss += unknown_normal_loss.item()
                val_unknown_abnormal_loss += unknown_abnormal_loss.item()
                val_batch_count += 1

        val_loss = val_loss / val_batch_count if val_batch_count > 0 else float('inf')
        val_known_loss = val_known_loss / val_batch_count if val_batch_count > 0 else 0.0
        val_unknown_normal_loss = val_unknown_normal_loss / val_batch_count if val_batch_count > 0 else 0.0
        val_unknown_abnormal_loss = val_unknown_abnormal_loss / val_batch_count if val_batch_count > 0 else 0.0
        
        logger.info(f"Validation Summary - "
                   f"Known normal samples: {val_total_known_samples}, "
                   f"Unknown normal samples: {val_total_unknown_normal_samples}, "
                   f"Unknown abnormal samples: {val_total_unknown_abnormal_samples}, "
                   f"Uncategorized samples: {val_total_uncategorized_samples}")

        logger.info(f"Epoch [{epoch+1}/{epoch_num}] - "
            f"Train Loss: {avg_loss:.4f} (Known: {avg_known_loss:.4f}, "
            f"Unknown Normal: {avg_unknown_normal_loss:.4f}, Unknown Abnormal: {avg_unknown_abnormal_loss:.4f}), "
            f"Val Loss: {val_loss:.4f} (Known: {val_known_loss:.4f}, "
            f"Unknown Normal: {val_unknown_normal_loss:.4f}, Unknown Abnormal: {val_unknown_abnormal_loss:.4f})")

        # Step the learning rate scheduler based on validation loss
        scheduler.step(val_loss)

        # Early Stopping
        if val_loss < best_val_loss:
            logger.info(f"Early Stopping: Validation loss improved from {best_val_loss:.4f} to {val_loss:.4f}")
            best_val_loss = val_loss
            best_model = copy.deepcopy(model)
            patience_counter = 0
            torch.save(model.state_dict(), model_file)
            logger.info(f"Model saved to {model_file}")
        else:
            patience_counter += 1
            logger.info(f"Early Stopping: No improvement. Patience {patience_counter}/{patience}, "
              f"Best val_loss: {best_val_loss:.4f}, Current: {val_loss:.4f}")

        if patience_counter >= patience:
            logger.info("Early stopping triggered")
            break

    # Calculate threshold only at the end of training for testing purposes
    best_threshold = compute_threshold(train_loader, best_model, percent, device)
    logger.info(f"Final threshold calculated for testing: {best_threshold:.6f}")

    return best_model, best_threshold

# ==========================
# Pure Reconstruction Training Function
# ==========================
def pure_train_generator(train_loader, validate_loader, batch_size, epoch_num, model_file=None, lr_rate=3e-4, device='cuda'):
    """
    Simple training function that only performs reconstruction on known normal data.
    No triplet loss or unknown data separation is performed.
    Mimics the structure and logic of the TF-based train_generator function.
    """
    # Create model
    model = TransformerAE(embed_dim=768, num_heads=12, hidden_dims=[512, 128], \
                          num_layers=2, max_seq_length=max_len, latent_dim=64).to(device)
    
    # Setup optimizer with learning rate scheduling
    # Similar to the TF optimization with warmup
    num_train_steps = len(train_loader) * epoch_num
    num_warmup_steps = int(0.0001 * num_train_steps)
    
    # Create optimizer
    optimizer = optim.AdamW(model.parameters(), lr=lr_rate)
    
    # Create scheduler similar to TF warmup
    def lr_lambda(current_step):
        if current_step < num_warmup_steps:
            return float(current_step) / float(max(1, num_warmup_steps))
        return max(0.0, float(num_train_steps - current_step) / float(max(1, num_train_steps - num_warmup_steps)))
    
    scheduler = optim.lr_scheduler.LambdaLR(optimizer, lr_lambda)
    
    # Loss function
    criterion = nn.MSELoss()

    best_val_loss = float('inf')
    best_model = None
    patience = 10
    patience_counter = 0
    
    # Store history similar to TF callbacks
    train_loss_history = []
    val_loss_history = []
    
    # Training Loop with similar structure to TF fit_generator
    for epoch in range(epoch_num):
        model.train()
        total_loss = 0
        batch_count = 0
        
        # Log current learning rate
        current_lr = optimizer.param_groups[0]['lr']
        logger.info(f"Epoch [{epoch+1}/{epoch_num}] - Current learning rate: {current_lr:.6f}")

        for batch in tqdm(train_loader, desc=f"Training Epoch {epoch+1}/{epoch_num}"):
            data, _, _ = batch  # Ignore labels as we're only using normal data
            data = data.to(device)

            optimizer.zero_grad()

            # Forward pass and get reconstruction
            outputs = model(data)
            loss = criterion(outputs, data)
            
            # Backward and optimize
            loss.backward()
            optimizer.step()
            scheduler.step()

            total_loss += loss.item()
            batch_count += 1

        # Calculate average loss
        avg_loss = total_loss / batch_count if batch_count > 0 else float('inf')
        train_loss_history.append(avg_loss)

        # Validation
        model.eval()
        val_loss = 0
        val_batch_count = 0
        
        with torch.no_grad():
            for batch in tqdm(validate_loader, desc="Validating"):
                data, _, _ = batch  # Ignore labels
                data = data.to(device)

                # Calculate reconstruction loss
                outputs = model(data)
                val_batch_loss = criterion(outputs, data)
                
                val_loss += val_batch_loss.item()
                val_batch_count += 1

        val_loss = val_loss / val_batch_count if val_batch_count > 0 else float('inf')
        val_loss_history.append(val_loss)

        logger.info(f"Epoch [{epoch+1}/{epoch_num}] - Training Loss: {avg_loss:.4f}, Validation Loss: {val_loss:.4f}")

        # Early Stopping - similar to TF EarlyStopping callback
        if val_loss < best_val_loss:
            logger.info(f"Validation loss improved from {best_val_loss:.4f} to {val_loss:.4f}")
            best_val_loss = val_loss
            best_model = copy.deepcopy(model)
            patience_counter = 0
            torch.save(model.state_dict(), model_file)
            logger.info(f"Model saved to {model_file}")
        else:
            patience_counter += 1
            logger.info(f"No improvement. Patience {patience_counter}/{patience}")

        if patience_counter >= patience:
            logger.info("Early stopping triggered")
            break

    # Calculate threshold from training data (similar to TF version)
    print("Calculating threshold from training data...")
    best_threshold = compute_threshold(train_loader, best_model, percent, device)
    
    # Save loss history and threshold
    threshold_path = model_file.replace('.pth', '_threshold.txt')
    with open(threshold_path, 'w') as f:
        f.write(str(best_threshold))
    
    loss_history_path = model_file.replace('.pth', '_loss_history.pkl')
    with open(loss_history_path, 'wb') as f:
        pickle.dump({'train_loss': train_loss_history, 'val_loss': val_loss_history}, f)

    return best_model, best_threshold

# ==========================
# Main Training Function
# ==========================
def train(known_normal_data, unknown_data, true_labels, epoch_num, batch_size, model_file=None, lr_rate=3e-4, device='cuda'):
    """
    Main function to train Transformer Autoencoder with Triplet Loss.
    - Uses known normal data and unknown data as input.
    - Trains using MSE + Triplet Loss.
    - Updates anomaly threshold dynamically per epoch.
    """

    # Merge known normal data with unknown data
    all_data = known_normal_data + unknown_data
    all_labels = [0] * len(known_normal_data) + [1] * len(unknown_data)
    all_true_labels = true_labels

    # Shuffle data before splitting
    all_data, all_labels, all_true_labels = shuffle(all_data, all_labels, all_true_labels)

    # Train-Validation Split
    train_size = int(0.8 * len(all_data))
    val_size = len(all_data) - train_size

    train_data, val_data = all_data[:train_size], all_data[train_size:]
    train_labels, val_labels = all_labels[:train_size], all_labels[train_size:]
    train_true_labels, val_true_labels = all_true_labels[:train_size], all_true_labels[train_size:]
    
    # Create datasets
    train_dataset = CustomDataset(train_data, train_labels, train_true_labels)
    val_dataset = CustomDataset(val_data, val_labels, val_true_labels)
    
    # Create balanced batch sampler for training
    # train_sampler = BalancedBatchSampler(train_dataset, batch_size, normal_ratio=0.4)
    # val_sampler = BalancedBatchSampler(val_dataset, batch_size, normal_ratio=0.4)
    
    # Create data loaders with custom sampler
    # training_loader = DataLoader(train_dataset, batch_sampler=train_sampler)
    # validate_loader = DataLoader(val_dataset, batch_sampler=val_sampler)

    training_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    validate_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=True)
    

    logger.info(f"Training Samples: {len(train_data)}, Validation Samples: {len(val_data)}")
    logger.info(f"Known Normal Training Samples: {sum(np.array(train_labels) == 0)}, Unknown Training Samples: {sum(np.array(train_labels) == 1)}")
    # Log number of unknown normal and abnormal samples in training set
    unknown_indices = np.where(np.array(train_labels) == 1)[0]
    unknown_normal_count = sum(np.array(train_true_labels)[unknown_indices] == 0)
    unknown_abnormal_count = sum(np.array(train_true_labels)[unknown_indices] == 1)
    logger.info(f"Unknown Training Samples - Normal: {unknown_normal_count}, Abnormal: {unknown_abnormal_count}")
    # Log validation set statistics in one line
    val_known_normal = sum(np.array(val_labels) == 0)
    val_unknown = sum(np.array(val_labels) == 1)
    val_unknown_indices = np.where(np.array(val_labels) == 1)[0]
    val_unknown_normal = sum(np.array(val_true_labels)[val_unknown_indices] == 0)
    val_unknown_abnormal = sum(np.array(val_true_labels)[val_unknown_indices] == 1)
    logger.info(f"Validation Set - Known Normal: {val_known_normal}, Unknown: {val_unknown} (Normal: {val_unknown_normal}, Abnormal: {val_unknown_abnormal})")
    # logger.info(f"Normal samples: {len(train_sampler.normal_indices)}, Unknown samples: {len(train_sampler.unknown_indices)}")
    # logger.info(f"Normal per batch: {train_sampler.normal_per_batch}, Unknown per batch: {train_sampler.unknown_per_batch}")
    # logger.info(f"Total batches: {train_sampler.num_batches}")

    # Train Model
    model, best_threshold = train_generator(training_loader, validate_loader, batch_size, epoch_num, model_file, lr_rate, device)

    return model, best_threshold

# ==========================
# Main Pure Training Function
# ==========================
def main_train_pure(known_normal_data, epoch_num, batch_size, model_file=None, lr_rate=3e-4, device='cuda'):
    """
    Main function to train Transformer Autoencoder using only known normal data.
    - Uses pure reconstruction loss (MSE) on known normal data
    - Avoids triplet loss and separation of unknown data
    - Designed for standard autoencoder training workflow
    """
    logger.info("Starting pure reconstruction training with known normal data only")
    
    # Create labels - all data are known normal (label 0)
    all_data = known_normal_data
    all_labels = [0] * len(known_normal_data)
    all_true_labels = [0] * len(known_normal_data)  # All are truly normal

    # Shuffle data before splitting
    all_data, all_labels, all_true_labels = shuffle(all_data, all_labels, all_true_labels)

    # Train-Validation Split
    train_size = int(0.8 * len(all_data))
    train_data, val_data = all_data[:train_size], all_data[train_size:]
    train_labels, val_labels = all_labels[:train_size], all_labels[train_size:]
    train_true_labels, val_true_labels = all_true_labels[:train_size], all_true_labels[train_size:]
    
    # Create datasets
    train_dataset = CustomDataset(train_data, train_labels, train_true_labels)
    val_dataset = CustomDataset(val_data, val_labels, val_true_labels)
    
    # Create data loaders - no need for balanced sampling since all data is normal
    training_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    validate_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=True)

    logger.info(f"Training Samples: {len(train_data)}, Validation Samples: {len(val_data)}")
    
    # Train Model using pure reconstruction approach
    model, best_threshold = pure_train_generator(
        training_loader, validate_loader, batch_size, epoch_num, 
        model_file=model_file, lr_rate=lr_rate, device=device
    )

    return model, best_threshold

# ==========================
# Testing & Evaluation
# ==========================
def test_model(model, x_te, y_te, true_labels, batch_size, threshold, device='cuda'):
    """
    Evaluates the trained model using:
    - Reconstruction Loss
    - Learned Anomaly Threshold
    - AUC, Precision, Recall, F1-score
    """
    logger.info("Testing Model...")
    test_dataset = CustomDataset(x_te, y_te, true_labels)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

    model.eval()
    all_losses = []
    all_true_labels = []
    loss_list = []
    with torch.no_grad():
        for batch in tqdm(test_loader, desc="Testing"):
            data, labels, true_labels = batch
            data = data.to(device)
            
            # Get model predictions
            outputs = model(data, return_latent=False)
            
            # Calculate MSE loss
            # error = torch.mean((data - outputs) ** 2, dim=2).mean(dim=1)
            # error = ((data - outputs) ** 2).mean(dim=2)
            # 
            error = nn.functional.mse_loss(outputs, data, reduction='none').mean(dim=(1, 2))

            loss_list.extend(error.tolist())
            all_true_labels.extend(true_labels.tolist())
      
    loss_list = np.array(loss_list)
    all_true_labels = np.array(all_true_labels)
    print(loss_list)
    print(loss_list.shape)
    # loss_list = loss_list.mean(axis=1)
    # print(loss_list.shape)
    # print(loss_list)
    predictions = (loss_list >= threshold).astype(int)

    # Calculate ROC curve and AUC
    # Count occurrences of each class in y_te
    normal_count = np.sum(y_te == 0)
    abnormal_count = np.sum(y_te == 1)
    logger.info(f"Test Dataset Summary - Normal samples: {normal_count}, Abnormal samples: {abnormal_count}")
    fpr, tpr, threshold_auto = roc_curve(all_true_labels, -loss_list, pos_label=0)
    print("Threshold", threshold_auto)
    auc_score = auc(fpr, tpr)

    # Calculate AUC score
    # auc_score = roc_auc_score(y_te, loss_list)
    
    # Calculate metrics
    precision, recall, f1_score, _ = precision_recall_fscore_support(all_true_labels, predictions, average='binary', pos_label=0)
    
    logger.info(f"Metrics - AUC: {auc_score:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}, F1 Score: {f1_score:.4f}")
    
    return auc_score

# ==========================
# Main Execution
# ==========================
if __name__ == '__main__':
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    logger.info(f"Using device: {device}")
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Transformer Autoencoder for Log Anomaly Detection')
    parser.add_argument('--mode', type=str, choices=['train', 'test'], required=True, 
                        help='Mode: train a new model or test an existing model')
    parser.add_argument('--pure', action='store_true', 
                        help='Use pure reconstruction training (default: False)')
    args = parser.parse_args()

    # Set model path
    model_path = f"../saved_model/{dataset}/{exp_no}_{model_name}_{max_len}_{dataset}_{name}_transformer.pth"
    logger.info(f"Model path: {model_path}")
    
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(model_path), exist_ok=True)

    # (x_tr, y_tr), (x_te, y_te) = torch_data_loader.load_supercomputers(
    #      log_file, train_ratio=0.4, windows_size=max_len,
    #      step_size=1, e_type='bert', mode='imbalance')

    # pickle.dump(x_tr, open(f"../../logs/{dataset}/encoded_data/{name}_x_tr.pkl", "wb"))
    # pickle.dump(y_tr, open(f"../../logs/{dataset}/encoded_data/{name}_y_tr.pkl", "wb"))
    # pickle.dump(x_te, open(f"../../logs/{dataset}/encoded_data/{name}_x_te.pkl", "wb"))
    # pickle.dump(y_te, open(f"../../logs/{dataset}/encoded_data/{name}_y_te.pkl", "wb"))

    # Load Data
    x_tr = pickle.load(open(f"../../logs/{dataset}/encoded_data/{name}_x_tr.pkl", "rb"))
    y_tr = pickle.load(open(f"../../logs/{dataset}/encoded_data/{name}_y_tr.pkl", "rb"))
    x_te = pickle.load(open(f"../../logs/{dataset}/encoded_data/{name}_x_te.pkl", "rb"))
    y_te = pickle.load(open(f"../../logs/{dataset}/encoded_data/{name}_y_te.pkl", "rb"))

    y_tr = np.array(y_tr)
    y_te = np.array(y_te)

    logger.info(f"Original training data - Normal: {sum(y_tr == 0)}, Abnormal: {sum(y_tr == 1)}")
    logger.info(f"Original testing data - Normal: {sum(y_te == 0)}, Abnormal: {sum(y_te == 1)}")

    # Keep all normal data and adjust abnormal data to maintain 4:1 ratio
    # Training data adjustment
    normal_tr_indices = np.where(y_tr == 0)[0]
    abnormal_tr_indices = np.where(y_tr == 1)[0]

    # Keep all normal samples, calculate max allowed abnormal samples
    max_allowed_abnormal_tr = len(normal_tr_indices) // 20
    
    # If we have more abnormal samples than allowed, downsample them
    if len(abnormal_tr_indices) > max_allowed_abnormal_tr:
        np.random.shuffle(abnormal_tr_indices)
        selected_abnormal_tr_indices = abnormal_tr_indices[:max_allowed_abnormal_tr]
    else:
        # Keep all abnormal samples if there aren't too many
        selected_abnormal_tr_indices = abnormal_tr_indices
    
    # Combine selected indices and shuffle
    selected_tr_indices = np.concatenate([normal_tr_indices, selected_abnormal_tr_indices])
    np.random.shuffle(selected_tr_indices)

    # Create balanced training set
    x_tr_balanced = [x_tr[i] for i in selected_tr_indices]
    y_tr_balanced = y_tr[selected_tr_indices]

    # Test data adjustment - same approach
    normal_te_indices = np.where(y_te == 0)[0]
    abnormal_te_indices = np.where(y_te == 1)[0]

    # Keep all normal test samples, calculate max allowed abnormal samples
    max_allowed_abnormal_te = len(normal_te_indices) // 20
    
    # If we have more abnormal samples than allowed, downsample them
    if len(abnormal_te_indices) > max_allowed_abnormal_te:
        np.random.shuffle(abnormal_te_indices)
        selected_abnormal_te_indices = abnormal_te_indices[:max_allowed_abnormal_te]
    else:
        # Keep all abnormal samples if there aren't too many
        selected_abnormal_te_indices = abnormal_te_indices
    
    # Combine selected indices and shuffle
    selected_te_indices = np.concatenate([normal_te_indices, selected_abnormal_te_indices])
    np.random.shuffle(selected_te_indices)

    # Create balanced test set
    x_te = [x_te[i] for i in selected_te_indices]
    y_te = y_te[selected_te_indices]

    logger.info(f"Adjusted training data - Normal: {sum(y_tr_balanced == 0)}, Abnormal: {sum(y_tr_balanced == 1)}")
    logger.info(f"Ratio in training: {sum(y_tr_balanced == 0) / sum(y_tr_balanced == 1):.2f}:1")
    logger.info(f"Adjusted testing data - Normal: {sum(y_te == 0)}, Abnormal: {sum(y_te == 1)}")
    logger.info(f"Ratio in testing: {sum(y_te == 0) / sum(y_te == 1):.2f}:1")

    # Split Normal Data into 50% Known Normal + 50% Unlabeled for training
    normal_samples = [x_tr_balanced[i] for i in range(len(y_tr_balanced)) if y_tr_balanced[i] == 0]
    abnormal_samples = [x_tr_balanced[i] for i in range(len(y_tr_balanced)) if y_tr_balanced[i] == 1]

    if args.pure:
        num_known_normal = len(normal_samples)  # All for known normal in pure mode
    else:
        num_known_normal = len(normal_samples) // 2  # 50% for known normal in triplet mode

    known_normal_data = normal_samples[:num_known_normal]
    unknown_data = normal_samples[num_known_normal:] + abnormal_samples
    true_labels = y_tr_balanced

    logger.info(f"Known Normal Samples: {len(known_normal_data)}, Unlabeled Samples: {len(unknown_data)}")
    logger.info(f"Unknown Normal Samples: {len(normal_samples[num_known_normal:])}, Unknown Abnormal Samples: {len(abnormal_samples)}")

    # Log testing data statistics
    normal_test_samples = sum(y_te == 0)
    abnormal_test_samples = sum(y_te == 1)
    logger.info(f"Testing Data Statistics:")
    logger.info(f"Total test samples: {len(y_te)}")
    logger.info(f"Normal test samples: {normal_test_samples} ({100 * normal_test_samples / len(y_te):.2f}%)")
    logger.info(f"Abnormal test samples: {abnormal_test_samples} ({100 * abnormal_test_samples / len(y_te):.2f}%)")

    if args.mode == 'train':
        logger.info("Mode: Training")
        # Choose training method based on args
        use_pure_reconstruction = args.pure
        
        if use_pure_reconstruction:
            logger.info("Using pure reconstruction training with known normal data only")
            model, best_threshold = main_train_pure(
                known_normal_data, 
                epoch_num=1000, 
                batch_size=128,
                model_file=model_path,
                lr_rate=0.0002, 
                device=device
            )
        else:
            logger.info("Using triplet loss training with known normal and unknown data")
            model, best_threshold = train(
                known_normal_data, 
                unknown_data, 
                true_labels, 
                epoch_num=1000, 
                batch_size=128, 
                model_file=model_path,
                lr_rate=0.0002, 
                device=device
            )
            
        # Save threshold to file
        threshold_path = model_path.replace('.pth', '_threshold.txt')
        with open(threshold_path, 'w') as f:
            f.write(str(best_threshold))
        logger.info(f"Threshold saved to: {threshold_path}")
            
        # Test the trained model
        auc_auto = test_model(model, x_te, y_te, y_te, batch_size=1024, threshold=best_threshold, device=device)
        logger.info(f"Testing complete. Final AUC: {auc_auto:.4f}")
    
    else:  # Test mode
        logger.info("Mode: Testing")
        # Check if model exists
        if not os.path.exists(model_path):
            logger.error(f"Model not found at {model_path}. Please train the model first.")
            sys.exit(1)
            
        # Load the trained model
        model = TransformerAE(embed_dim=768, num_heads=12, hidden_dims=[512, 128], 
                             num_layers=2, max_seq_length=max_len, latent_dim=64).to(device)
        model.load_state_dict(torch.load(model_path))
        logger.info(f"Model loaded from {model_path}")
        
        # Load threshold if available, otherwise compute it
        threshold_path = model_path.replace('.pth', '_threshold.txt')
        if os.path.exists(threshold_path):
            with open(threshold_path, 'r') as f:
                best_threshold = float(f.read().strip())
            logger.info(f"Threshold loaded: {best_threshold}")
        else:
            # Create a loader with only known normal data for threshold computation
            logger.info("Threshold file not found, computing threshold...")
            normal_dataset = CustomDataset(known_normal_data, [0]*len(known_normal_data), [0]*len(known_normal_data))
            normal_loader = DataLoader(normal_dataset, batch_size=128, shuffle=False)
            best_threshold = compute_threshold(normal_loader, model, percent, device)
            
        # Test the model
        auc_auto = test_model(model, x_te, y_te, y_te, batch_size=1024, threshold=best_threshold, device=device)
        logger.info(f"Testing complete. Final AUC: {auc_auto:.4f}")
