import pickle
import numpy as np
import matplotlib.pyplot as plt
from sklearn.manifold import TSNE

# Paths to latent files (update as needed)
train_latent_path = "../saved_data/SPIRIT/latents/SPIRIT_full_log_tr0.8_oov0.2_uar0.025_triplet_classifier_w20_s20/latent_train.pkl"
test_latent_path = "../saved_data/SPIRIT/latents/SPIRIT_full_log_tr0.8_oov0.2_uar0.025_triplet_classifier_w20_s20/latent_test.pkl"

# Load latent representations and labels
with open(train_latent_path, "rb") as f:
    train_data = pickle.load(f)
with open(test_latent_path, "rb") as f:
    test_data = pickle.load(f)

train_latents = train_data['latents']
train_labels = np.array(train_data['labels'])
test_latents = test_data['latents']
test_labels = np.array(test_data['labels'])

rng = np.random.default_rng(1234)

def sample_latents(latents, labels, n_normal=2000, n_abnormal=2000):
    normal_idx = np.where(labels == 0)[0]
    abnormal_idx = np.where(labels == 1)[0]
    n_normal = min(n_normal, len(normal_idx))
    n_abnormal = min(n_abnormal, len(abnormal_idx))
    normal_sample_idx = rng.choice(normal_idx, n_normal, replace=False) if n_normal > 0 else np.array([], dtype=int)
    abnormal_sample_idx = rng.choice(abnormal_idx, n_abnormal, replace=False) if n_abnormal > 0 else np.array([], dtype=int)
    idx = np.concatenate([normal_sample_idx, abnormal_sample_idx])
    return latents[idx], labels[idx]

# Sample for train and test
train_latents_vis, train_labels_vis = sample_latents(train_latents, train_labels)
test_latents_vis, test_labels_vis = sample_latents(test_latents, test_labels)

# T-SNE for train and test combined
all_latents_vis = np.concatenate([train_latents_vis, test_latents_vis], axis=0)
all_labels_vis = np.concatenate([train_labels_vis, test_labels_vis], axis=0)
all_domain_vis = np.array([0] * len(train_latents_vis) + [1] * len(test_latents_vis))  # 0: train, 1: test

tsne = TSNE(n_components=2, random_state=1234, init='pca', perplexity=30, n_iter=1000, verbose=1, n_jobs=-1)
latents_2d = tsne.fit_transform(all_latents_vis.astype(np.float32))

# Shift test set to the right for clear separation
latents_2d_train = latents_2d[all_domain_vis == 0]
latents_2d_test = latents_2d[all_domain_vis == 1]

# Compute shift value (distance between max train and min test on x-axis, plus margin)
shift_margin = 10
if len(latents_2d_train) > 0 and len(latents_2d_test) > 0:
    shift_value = latents_2d_train[:, 0].max() - latents_2d_test[:, 0].min() + shift_margin
    latents_2d[all_domain_vis == 1, 0] += shift_value

# Plot as two subfigures: left for training, right for testing
fig, axes = plt.subplots(1, 2, figsize=(16, 7), sharey=True)

# Training subplot (left)
for label, color, marker, name in zip([0, 1], ['blue', 'red'], ['o', 'x'], ['Normal', 'Abnormal']):
    idx_train = (all_labels_vis == label) & (all_domain_vis == 0)
    axes[0].scatter(
        latents_2d[idx_train, 0], latents_2d[idx_train, 1],
        c=color, marker=marker, alpha=0.6, label=name
    )
axes[0].set_title("Training TripletLog Latent Representations")
axes[0].set_xlabel("TSNE-1")
axes[0].set_ylabel("TSNE-2")
axes[0].legend()
axes[0].grid(True)

# Testing subplot (right)
for label, color, marker, name in zip([0, 1], ['blue', 'red'], ['o', 'x'], ['Normal', 'Abnormal']):
    idx_test = (all_labels_vis == label) & (all_domain_vis == 1)
    axes[1].scatter(
        latents_2d[idx_test, 0], latents_2d[idx_test, 1],
        c=color, marker=marker, alpha=0.6, label=name
    )
axes[1].set_title("Testing TripletLog Latent Representations")
axes[1].set_xlabel("TSNE-1")
axes[1].set_ylabel("TSNE-2")
axes[1].legend()
axes[1].grid(True)

plt.tight_layout()
plt.savefig("tripletlog_latent_SPIRIT.pdf", dpi=300)
plt.show()
