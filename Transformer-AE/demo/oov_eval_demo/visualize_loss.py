import numpy as np
import pickle
import matplotlib.pyplot as plt

root_path = '../../demo/'
training_losses_file = open('TDB_training_losses.pkl', 'rb')
valid_losses_file = open('TDB_validating_losses.pkl', 'rb')

training_losses = pickle.load(training_losses_file)
valid_losses = pickle.load(valid_losses_file)
print(training_losses)
epochs = range(0, len(training_losses))
plt.plot(epochs, training_losses, 'g', label='Training loss')
plt.plot(epochs, valid_losses, 'b', label='validation loss')
plt.title('Training and Validation loss')
plt.xlabel('Epochs')
plt.ylabel('Loss')
plt.legend()
plt.savefig('TDB_loss')