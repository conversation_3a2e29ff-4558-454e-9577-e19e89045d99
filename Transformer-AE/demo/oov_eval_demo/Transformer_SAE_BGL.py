import os
import sys
import gc
import datetime
sys.path.append("../")
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2' 
# os.environ['CUDA_VISIBLE_DEVICES'] = ""
# 0 = all messages are logged (default behavior)
# 1 = INFO messages are not printed
# 2 = INFO and WARNING messages are not printed
# 3 = INFO, WARNING, and ERROR messages are not printed
import math
import pickle
import numpy as np
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras.utils import Sequence
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping
from tensorflow.keras.losses import SparseCategoricalCrossentropy, MeanSquaredError
from official.nlp import optimization
from sklearn.utils import shuffle

from neurallog.models import NeuralLog, AutoEncoder, TransformerSAE
from neurallog import main_data_loader
from neurallog.utils import classification_report, occ_get_score

from sklearn.metrics import roc_curve, auc
from sklearn.svm import SVC
from sklearn.svm import OneClassSVM
from sklearn.neighbors import LocalOutlierFactor
from sklearn.ensemble import IsolationForest

import multiprocessing

# num_processors = multiprocessing.cpu_count()
# pool = multiprocessing.Pool(processes=num_processors)

embed_dim = 768  # Embedding size for each token
max_len = 50
data_name = 'BGL'
oov_ratio = str(0.1 * 100)

class BatchGenerator(Sequence):

    def __init__(self, X, Y, batch_size):
        self.X, self.Y = X, Y
        self.batch_size = batch_size

    def __len__(self):
        return int(np.ceil(len(self.X) / float(self.batch_size)))

    def __getitem__(self, idx):
        # print(self.batch_size)
        dummy = np.zeros(shape=(embed_dim,))
        x = self.X[idx * self.batch_size:min((idx + 1) * self.batch_size, len(self.X))] # len(X) == batch_size; x is a batch has index idx: each record contains window_size messages

        X = np.zeros((len(x), max_len, embed_dim))      # X is 

        Y = np.zeros((len(x), 2))
        item_count = 0     # index for sequence in batch

        # loop each sequence in batch
        # resize sequence and padding to original sequences have window_size to max_len
        for i in range(idx * self.batch_size, min((idx + 1) * self.batch_size, len(self.X))):
            x = self.X[i]
            if len(x) > max_len:
                x = x[-max_len:]
            # padding the first (max_len-len(x)) elements
            x = np.pad(np.array(x), pad_width=((max_len - len(x), 0), (0, 0)), mode='constant',
                       constant_values=0)
            X[item_count] = np.reshape(x, [max_len, embed_dim])
            # Y[item_count] = np.reshape(x, [max_len, embed_dim])
            item_count += 1

        return X[:], X[:]

def test_model(model, x, y, batch_size):
    x, y = shuffle(x, y)

    # x, y = x[: len(x) // batch_size * batch_size], y[: len(y) // batch_size * batch_size]
    # print(x)
    test_loader = BatchGenerator(x, y, batch_size)
    
    inter_model = keras.Model(inputs=model.input, outputs=model.layers[8].output)

    loss_list = []
    num = 0
    for batch_in in test_loader:
        num +=1 
        # print("Batch {} / {}".format(num, math.ceil(len(x)/batch_size)), end='')
        outputs = inter_model.predict_generator(batch_in[0], steps=1)
        loss = tf.keras.losses.mse(outputs, batch_in[0])
        loss_list.append(loss)
    
    loss_list = tf.concat(loss_list, axis=0)
    loss = tf.math.reduce_mean(tf.constant(loss_list), axis=1)
    gc.collect()

    FPR_auto, TPR_auto, thresholds_auto = roc_curve(y, loss)
    auc_auto = auc(FPR_auto, TPR_auto)
    print("AUC - Transformer Autoencoder: {}".format(auc_auto))
    
    return auc_auto
    # del y
    # # del prediction
    # del loss
    # gc.collect()


def train_generator(training_generator, validate_generator, x_te, y_te, num_train_samples, num_val_samples, batch_size,
                    epoch_num, model_file=None, lr_rate=3e-4, cache=False):
    epochs = epoch_num
    steps_per_epoch = num_train_samples
    num_train_steps = steps_per_epoch * epochs
    num_warmup_steps = int(0.1 * num_train_steps)

    init_lr = 3e-4
    optimizer = optimization.create_optimizer(init_lr=lr_rate,
                                              num_train_steps=num_train_steps,
                                              num_warmup_steps=num_warmup_steps,
                                              optimizer_type='adamw')
    def shrink_loss(y_true, y_pred):
        # Compute the shrink regularizer
        shrink_loss = tf.reduce_mean(tf.square(y_pred))
        return shrink_loss
    
    # return loss
    class LossHistory(keras.callbacks.Callback):
        def on_train_begin(self, logs={}):
            self.losses = []
            self.val_losses = []

        def on_epoch_end(self, epoch, logs={}):
            self.losses.append(logs.get('loss'))
            self.val_losses.append(logs.get('val_loss'))

    class EpochEvaluation(keras.callbacks.Callback):
        def __init__(self, X, Y, batch_size):
            super().__init__()
            self.X = X
            self.Y = Y
            self.batch_size = batch_size
            self.auc = []

        def on_epoch_end(self, epoch, logs=None):
            auc_auto = test_model(self.model, self.X, self.Y, self.batch_size)
            self.auc.append(auc_auto)
            
    if cache == True:
        # model = TransformerSAE(768, ff_dim=2048, max_len=max_len, num_heads=12, dropout=0.1)
        
        model = keras.models.load_model(model_file, compile=False)
        print("Loaded saved model from {}".format(model_file))
    else:
        model = TransformerSAE(768, ff_dim=1024, max_len=max_len, num_heads=12, dropout=0.1)
    
    
    model.compile(loss=[keras.losses.MeanSquaredError(), shrink_loss], 
                loss_weights=[1.0, 0.0],
                optimizer=optimizer)

    # checkpoint
    filepath = model_file
    checkpoint = ModelCheckpoint(filepath,
                                 monitor='val_loss',
                                 verbose=1,
                                 save_best_only=True,
                                 mode='min')
    early_stop = EarlyStopping(
        monitor='val_loss', min_delta=0, patience=3, verbose=0, mode='auto',
        baseline=None, restore_best_weights=True
    )

    # loss_save = LossHistory()
    auc_save = EpochEvaluation(x_te, y_te, 128)

    callbacks_list = [checkpoint, early_stop, auc_save]

    history = model.fit_generator(generator=training_generator,
                        steps_per_epoch=int(num_train_samples / batch_size),
                        epochs=epoch_num,
                        verbose=1,
                        validation_data=validate_generator,
                        validation_steps=int(num_val_samples / batch_size),
                        workers=16,
                        max_queue_size=32,
                        callbacks=callbacks_list,
                        shuffle=False
                        )
    
    pickle.dump(history.history['loss'], open('saved_data/oov_evaluation/{}_{}_training_losses.pkl'.format(data_name, oov_ratio), 'wb'))
    pickle.dump(history.history['val_loss'], open('saved_data/oov_evaluation/{}_{}_validating_losses.pkl'.format(data_name, oov_ratio), 'wb'))
    pickle.dump(auc_save.auc, open('saved_data/oov_evaluation/{}_{}_epoch_auc.pkl'.format(data_name, oov_ratio), 'wb'))

    print(model.summary())

    return model

def train(X, Y, x_te, y_te, epoch_num, batch_size, model_file=None, model_type=None, lr_rate=3e-4, cache=False):
    # if cache == True:
    #     # model = TransformerSAE(768, ff_dim=2048, max_len=max_len, num_heads=12, dropout=0.1)

    #     model = keras.models.load_model(model_file, compile=False)
    #     print(model.summary())
        # return model
    
    X, Y = shuffle(X, Y)
    n_samples = len(X)

    train_x, train_y = X[:int(n_samples * 90 / 100)], Y[:int(n_samples * 90 / 100)]
    val_x, val_y = X[int(n_samples * 90 / 100):], Y[int(n_samples * 90 / 100):]

    training_generator, num_train_samples = BatchGenerator(train_x, train_x, batch_size), len(train_x)
    validate_generator, num_val_samples = BatchGenerator(val_x, val_x, batch_size), len(val_x)

    print("Number of training samples: {0} - Number of validating samples: {1}".format(num_train_samples,
                                                                                    num_val_samples))

    model = train_generator(training_generator, validate_generator, x_te, y_te, num_train_samples, num_val_samples, batch_size,
                            epoch_num, model_file=model_file, lr_rate=lr_rate, cache=cache)

    return model


def get_latent(model, x, batch_size):
    data_loader = BatchGenerator(x, x, batch_size=batch_size)

    inter_model = keras.Model(inputs=model.input, outputs=model.layers[5].output)
    print(inter_model.summary())
    latent = inter_model.predict_generator(data_loader, steps=math.ceil(len(x) / batch_size), verbose=1)

    return latent


def ocsvm_01_eval(latent_train, latent_test, y_test, occ_score): 
    latent_train = tf.math.reduce_mean(latent_train, axis=1)
    latent_test = tf.math.reduce_mean(latent_test, axis=1)

    oc_svm = OneClassSVM(nu=0.1)
    print("OCSVM-0.1 training --- ")
    oc_svm.fit(latent_train)
    del latent_train
    print("OCSVM-0.1 predicting --- ")

    # chunk_size = len(latent_test) // num_processors
    # chunks = [latent_test[i:i+chunk_size] for i in range(0, len(latent_test), chunk_size)]

    # scores = pool.starmap(occ_score, [(chunk, oc_svm) for chunk in chunks])

    # concated_scores = np.concatenate(scores)

    concated_scores = oc_svm.decision_function(latent_test)

    # scores = oc_svm.decision_function(latent_test)

    FPR_ocsvm, TPR_ocsvm, thresholds_ocsvm = roc_curve(y_test, -concated_scores, pos_label=1)    # - is that: negative is for inliers
    auc_ocsvm = auc(FPR_ocsvm, TPR_ocsvm)
    print("AUC - OCSVM - 0.1: {}".format(auc_ocsvm))
    
    del latent_test
    del oc_svm
    del auc_ocsvm
    # del chunks
    # del scores, concated_scores
    gc.collect()


def ocsvm_05_eval(latent_train, latent_test, y_test, occ_score): 
    latent_train = tf.math.reduce_mean(latent_train, axis=1)
    latent_test = tf.math.reduce_mean(latent_test, axis=1)

    oc_svm = OneClassSVM(nu=0.5)
    print("OCSVM-0.5 training --- ")
    oc_svm.fit(latent_train)
    del latent_train
    print("OCSVM-0.5 predicting --- ")

    # chunk_size = len(latent_test) // num_processors
    # chunks = [latent_test[i:i+chunk_size] for i in range(0, len(latent_test), chunk_size)]

    # scores = pool.starmap(occ_score, [(chunk, oc_svm) for chunk in chunks])

    # concated_scores = np.concatenate(scores)

    concated_scores = oc_svm.decision_function(latent_test)
    # scores = oc_svm.decision_function(latent_test)

    FPR_ocsvm, TPR_ocsvm, thresholds_ocsvm = roc_curve(y_test, -concated_scores, pos_label=1)    # - is that: negative is for inliers
    auc_ocsvm = auc(FPR_ocsvm, TPR_ocsvm)
    print("AUC - OCSVM-0.5: {}".format(auc_ocsvm))
    
    # del latent_test, chunks, concated_scores
    del oc_svm
    # del scores
    del auc_ocsvm
    gc.collect()

def lof_eval(latent_train, latent_test, y_test, occ_score): 
    latent_train = tf.math.reduce_mean(latent_train, axis=1)
    latent_test = tf.math.reduce_mean(latent_test, axis=1)

    lof = LocalOutlierFactor(n_neighbors=20, novelty=True)
    print("LOF training --- ")
    lof.fit(latent_train)
    del latent_train
    print("LOF predicting --- ")

    # chunk_size = len(latent_test) // num_processors
    # chunks = [latent_test[i:i+chunk_size] for i in range(0, len(latent_test), chunk_size)]

    # scores = pool.starmap(occ_score, [(chunk, lof) for chunk in chunks])

    # concated_scores = np.concatenate(scores)

    concated_scores = lof.decision_function(latent_test)

    # scores = lof.decision_function(latent_test)

    FPR_ocsvm, TPR_ocsvm, thresholds_ocsvm = roc_curve(y_test, -concated_scores, pos_label=1)    # - is that: negative is for inliers
    auc_ocsvm = auc(FPR_ocsvm, TPR_ocsvm)
    print("AUC - LOF: {}".format(auc_ocsvm))
    
    # del latent_test, chunks, concated_scores
    del lof
    # del scores
    del auc_ocsvm
    gc.collect()

def iso_eval(latent_train, latent_test, y_test, occ_score): 
    latent_train = tf.math.reduce_mean(latent_train, axis=1)
    latent_test = tf.math.reduce_mean(latent_test, axis=1)

    iso = IsolationForest(random_state=42)
    print("IF training --- ")
    iso.fit(latent_train)
    del latent_train
    print("IF predicting --- ")

    # chunk_size = len(latent_test) // num_processors
    # chunks = [latent_test[i:i+chunk_size] for i in range(0, len(latent_test), chunk_size)]

    # scores = pool.starmap(occ_score, [(chunk, iso) for chunk in chunks])

    # concated_scores = np.concatenate(scores)

    concated_scores = iso.decision_function(latent_test)
    # scores = iso.decision_function(latent_test)

    FPR_ocsvm, TPR_ocsvm, thresholds_ocsvm = roc_curve(y_test, -concated_scores, pos_label=1)    # - is that: negative is for inliers
    auc_ocsvm = auc(FPR_ocsvm, TPR_ocsvm)
    print("AUC - ISOLATION FOREST: {}".format(auc_ocsvm))
    pool.close()
    # del latent_test, chunks, concated_scores
    del iso
    # del scores
    del auc_ocsvm
    gc.collect()

if __name__ == '__main__':
    tf.debugging.set_log_device_placement(False)
    
    model_name = 'AE'
    name = "1m"
    log_file = "../logs/BGL/1m"
    model_file = 'BGL_transformer_autoencoder.hdf5'

    train_file = "../oov_evaluation/data/raw_data/BGL/processed/train.log"
    test_path = "../oov_evaluation/data/raw_data/BGL/processed/"
    file_name = "injected_test_10.0.log"
    test_file = test_path + file_name
    # with tf.device('/cpu:0'):
    (x_tr, y_tr), (x_te, y_te) = main_data_loader.load_supercomputers(
        train_file, test_file, windows_size=max_len,
        step_size=5, e_type='bert', mode='imbalance')

    pickle.dump(x_tr, open("../logs/BGL/oov_evaluation/encoded_data/{}_{}_x_tr.pkl".format(name, file_name), "wb"))
    pickle.dump(y_tr, open("../logs/BGL/oov_evaluation/encoded_data/{}_{}_y_tr.pkl".format(name, file_name), "wb"))
    pickle.dump(x_te, open("../logs/BGL/oov_evaluation/encoded_data/{}_{}_x_te.pkl".format(name, file_name), "wb"))
    pickle.dump(y_te, open("../logs/BGL/oov_evaluation/encoded_data/{}_{}_y_te.pkl".format(name, file_name), "wb"))
    
    # x_tr = pickle.load(open("../logs/BGL/oov_evaluation/encoded_data/{}_{}_x_tr.pkl".format(name, file_name), "rb"))
    # y_tr = pickle.load(open("../logs/BGL/oov_evaluation/encoded_data/{}_{}_y_tr.pkl".format(name, file_name), "rb"))
    # x_te = pickle.load(open("../logs/BGL/oov_evaluation/encoded_data/{}_{}_x_te.pkl".format(name, file_name), "rb"))
    # y_te = pickle.load(open("../logs/BGL/oov_evaluation/encoded_data/{}_{}_y_te.pkl".format(name, file_name), "rb"))

    # get only normal data for training _ set
    x_tr = [x_tr[i] for i in range(len(y_tr)) if y_tr[i] == 0 ]
    y_tr = [i for i in y_tr if i == 0]
    print('Nomal training set: {}'.format(len(x_tr)))

    cache = True
    saved_model_path = "best_model/AE_22:56:03.231872_50_5_bgl_1m_transformer.ckpt"
    if cache:
        # model = train(x_tr, y_tr, x_te, y_te, epoch_num=1, batch_size=128, model_file=saved_model_path, \
        #                       model_type='transformer', lr_rate=0.0001, cache=True)
        model = keras.models.load_model(saved_model_path, compile=False)
        print("Loaded saved model from {}".format(model_file))
    else: 
        model = train(x_tr, y_tr, x_te, y_te, epoch_num=1000, batch_size=128, model_file="saved_model/oov_evaluation/{}/{}_{}_50_5_BGL_1m_transformer.ckpt".format(data_name, model_name, oov_ratio), \
                              model_type='transformer', lr_rate=0.001, cache=False)
    
    
    
    # model = train(x_tr, y_tr, epoch_num=1, batch_size=128, model_file="saved_model/AE_17:01:04.395630_50_5_BGL_1m_transformer.ckpt", \
    #                           model_type='transformer', lr_rate=0.0001, cache=True)   
    
    auc_auto = test_model(model, x_te, y_te, batch_size=128)

    print("Get latent representation")
    latent_train = get_latent(model, x_tr, batch_size=64)
    # pickle.dump(latent_train, open('../logs/BGL/latent_AE/{}_train.pkl'.format(name), "wb"))
    # del latent_train
    latent_test = get_latent(model, x_te, batch_size=64)
    # pickle.dump(latent_test, open('../logs/BGL/latent_AE/{}_test.pkl'.format(name), "wb"))

    # ocsvm_01_eval(latent_train=latent_train, latent_test=latent_test, y_test=y_te, occ_score=occ_get_score)
    # ocsvm_05_eval(latent_train=latent_train, latent_test=latent_test, y_test=y_te, occ_score=occ_get_score)
    # lof_eval(latent_train=latent_train, latent_test=latent_test, y_test=y_te, occ_score=occ_get_score)
    # iso_eval(latent_train=latent_train, latent_test=latent_test, y_test=y_te, occ_score=occ_get_score)
